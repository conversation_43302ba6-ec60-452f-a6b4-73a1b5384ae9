# Discourse SSO Integration Sequence Diagrams

This document provides detailed sequence diagrams for the bidirectional SSO integration between our application and Discourse forum.

## Table of Contents

- [Forward SSO Flow (App → Discourse)](#forward-sso-flow-app--discourse)
- [Reverse SSO Flow (Discourse → App)](#reverse-sso-flow-discourse--app)
- [Error Handling Flows](#error-handling-flows)

## Forward SSO Flow (App → Discourse)

This flow is initiated when an authenticated user in our application clicks the "Community" button to access the Discourse forum.

```mermaid
sequenceDiagram
    participant User as User
    participant FE as NextJS Frontend
    participant API as NextJS API
    participant BE as Strapi Backend
    participant Discourse as Discourse Forum

    Note over User,Discourse: Forward SSO Flow (App → Discourse) - Correct Implementation

    %% User clicks Community button
    User->>FE: Clicks "Community" button in header

    %% Frontend checks authentication
    FE->>FE: Check if user is authenticated

    alt User not authenticated
        FE->>User: Redirect to login page
        User->>FE: Complete authentication
    end

    %% Dispatch Redux action
    FE->>FE: dispatch(discourseActions.getDiscourseSSOUrl())

    %% Saga intercepts action (forward flow)
    FE->>FE: Saga: handleForwardSSO()

    %% Frontend API call to get login URL
    FE->>API: POST /api/discourse
    Note over FE,API: {action: "get-login-url"}

    %% API calls Strapi for login URL
    API->>BE: GET /api/discourse/login-url

    %% Backend returns Discourse login URL
    BE->>API: Return Discourse login URL
    Note over BE,API: {loginUrl: "https://discourse.com/login"}

    API->>FE: Return login URL

    %% Frontend redirects user to Discourse
    FE->>User: Open Discourse login URL in new tab
    User->>Discourse: Visit Discourse login page
    Note over User,Discourse: GET /login

    %% User clicks login on Discourse
    User->>Discourse: Clicks "Log In" button on Discourse

    %% Discourse initiates SSO flow
    Discourse->>Discourse: Generate nonce and SSO payload
    Discourse->>Discourse: Create HMAC signature
    Discourse->>BE: Redirect to our SSO endpoint
    Note over Discourse,BE: GET /api/discourse/sso?sso=base64_nonce&sig=hmac_signature

    %% Backend handles initial SSO request
    BE->>BE: Validate HMAC signature from Discourse
    BE->>BE: Extract nonce from Discourse payload
    BE->>FE: Redirect to login page with SSO parameters
    Note over BE,FE: Redirect to /authentication?sso=...&sig=...&discourse=true

    %% User authenticates in our app
    FE->>User: Display login form (if not already authenticated)
    User->>FE: Enter credentials (if needed)
    FE->>BE: Authenticate user
    BE->>FE: Return JWT token

    %% Process SSO parameters with authenticated user
    FE->>API: POST /api/discourse
    Note over FE,API: {action: "process-sso-params", token: "jwt", sso: "...", sig: "..."}

    API->>BE: GET /api/discourse/sso-url?sso=...&sig=...
    Note over API,BE: Headers: {Authorization: "Bearer jwt"}

    %% Backend generates SSO response
    BE->>BE: Validate JWT token and SSO parameters
    BE->>BE: Extract user information
    BE->>BE: Generate SSO response with Discourse nonce
    Note over BE: Response includes:<br/>- nonce (from Discourse)<br/>- external_id<br/>- email<br/>- username<br/>- name<br/>- avatar_url (optional)

    BE->>BE: Create HMAC-SHA256 signature
    BE->>BE: Base64 encode response payload

    %% Return SSO response URL
    BE->>API: Return SSO response URL
    API->>FE: Return SSO response URL

    %% Frontend redirects back to Discourse
    FE->>User: Redirect to Discourse with SSO response
    User->>Discourse: Access with SSO response parameters
    Note over User,Discourse: GET /session/sso_login?sso=base64_response&sig=hmac_signature

    %% Discourse validates and logs in user
    Discourse->>Discourse: Validate HMAC signature
    Discourse->>Discourse: Verify nonce matches original request
    Discourse->>Discourse: Decode user payload
    Discourse->>Discourse: Create/update user account
    Discourse->>User: Log user into Discourse forum

    Note over User,Discourse: User is now logged into Discourse<br/>following the official SSO specification
```

## Reverse SSO Flow (Discourse → App)

This flow is initiated when a user visits the Discourse forum directly and clicks the "Log In" button.

```mermaid
sequenceDiagram
    participant User as User
    participant Discourse as Discourse Forum
    participant BE as Strapi Backend
    participant FE as NextJS Frontend
    participant API as NextJS API

    Note over User,API: Reverse SSO Flow (Discourse → App)

    %% User initiates login on Discourse
    User->>Discourse: Visits Discourse forum directly
    User->>Discourse: Clicks "Log In" button
    
    %% Discourse redirects to our SSO endpoint
    Discourse->>Discourse: Generate SSO payload with nonce
    Discourse->>Discourse: Create HMAC signature
    Discourse->>BE: Redirect to our SSO endpoint
    Note over Discourse,BE: GET /api/discourse/sso?sso=base64_payload&sig=hmac_signature
    
    %% Backend validates signature
    BE->>BE: Validate HMAC signature
    BE->>BE: Decode SSO payload
    BE->>BE: Extract nonce and return_sso_url
    
    %% Backend redirects to frontend login
    BE->>FE: Redirect to login page with SSO parameters
    Note over BE,FE: GET /authentication?sso=base64_payload&sig=hmac_signature&discourse=true
    
    %% User authenticates in our app
    FE->>FE: Detect Discourse SSO parameters
    FE->>User: Display login form
    User->>FE: Enter credentials
    FE->>API: Submit login credentials
    API->>BE: Authenticate user
    BE->>API: Return JWT token + user data
    API->>FE: Return authentication result
    
    %% After successful login
    FE->>FE: Check for stored Discourse SSO parameters
    FE->>FE: dispatch(discourseActions.getDiscourseSSOUrl({sso, sig}))
    
    %% Get SSO URL with authenticated user
    FE->>API: POST /api/discourse
    Note over FE,API: {action: "get-sso-url", token: "jwt_token", sso: "base64_payload", sig: "hmac_signature"}
    
    API->>BE: POST /api/discourse/sso-url?sso=base64_payload&sig=hmac_signature
    Note over API,BE: Headers: {Authorization: "Bearer jwt_token"}
    
    %% Backend generates response
    BE->>BE: Validate JWT token and SSO parameters
    BE->>BE: Extract user information
    BE->>BE: Generate SSO response with user data
    BE->>BE: Create HMAC signature for response
    BE->>BE: Base64 encode response payload
    
    %% Return and redirect
    BE->>API: Return SSO URL with response payload
    API->>FE: Return SSO URL
    
    %% Frontend redirects to Discourse
    FE->>User: Redirect to Discourse SSO URL
    Note over FE,User: window.location.href = ssoUrl
    
    User->>Discourse: Access with SSO response parameters
    
    %% Discourse validates and logs in user
    Discourse->>Discourse: Validate HMAC signature
    Discourse->>Discourse: Decode user payload
    Discourse->>Discourse: Create/update user account
    Discourse->>User: Log user into Discourse forum
    
    Note over User,Discourse: User is now logged into Discourse<br/>after authenticating in our application
```

## Error Handling Flows

### Authentication Failure

```mermaid
sequenceDiagram
    participant User as User
    participant FE as NextJS Frontend
    participant BE as Strapi Backend
    participant Discourse as Discourse Forum

    Note over User,Discourse: Authentication Failure Flow

    %% Initial SSO request from Discourse
    Discourse->>BE: GET /api/discourse/sso?sso=base64_payload&sig=hmac_signature
    BE->>FE: Redirect to login page with SSO parameters
    
    %% Authentication failure
    FE->>User: Display login form
    User->>FE: Enter invalid credentials
    FE->>BE: Authentication request fails
    BE->>FE: Return authentication error
    
    %% Error handling
    FE->>User: Display authentication error
    FE->>FE: Store SSO parameters in session
    
    %% User retries
    User->>FE: Enter correct credentials
    FE->>BE: Authentication succeeds
    BE->>FE: Return JWT token
    
    %% Continue with SSO flow
    FE->>BE: Request SSO URL with stored parameters
    BE->>FE: Return SSO URL
    FE->>Discourse: Redirect to Discourse
    Discourse->>User: Log user into Discourse
```

### Invalid SSO Signature

```mermaid
sequenceDiagram
    participant User as User
    participant FE as NextJS Frontend
    participant BE as Strapi Backend
    participant Discourse as Discourse Forum

    Note over User,Discourse: Invalid SSO Signature Flow

    %% Initial SSO request with invalid signature
    Discourse->>BE: GET /api/discourse/sso?sso=base64_payload&sig=invalid_signature
    
    %% Backend validation fails
    BE->>BE: HMAC validation fails
    BE->>FE: Redirect to login with error
    
    %% Error display
    FE->>User: Display "Invalid SSO request" error
    FE->>User: Provide link to Discourse home page
    
    %% User returns to Discourse
    User->>Discourse: Navigate back to Discourse
    User->>Discourse: Attempt login again
```

These sequence diagrams provide a detailed visualization of the bidirectional SSO flows between our application and Discourse, including the specific API endpoints, data exchanges, and user interactions at each step of the process.
