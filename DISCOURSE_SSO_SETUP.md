# Discourse SSO Integration Setup Guide

This guide explains how to set up and test the Discourse Single Sign-On (SSO) integration between your Strapi backend and NextJS frontend.

## Overview

The implementation provides **bidirectional SSO integration** between your application and Discourse:

1. **Forward SSO**: Authenticated users in your app can access Discourse via the Community button
2. **Reverse SSO**: Users can initiate login from Discourse and be redirected to your app for authentication

## Architecture

The bidirectional SSO integration supports two distinct flows:

### Forward SSO Flow (App → Discourse) - CORRECTED
```
User → NextJS Frontend → Discourse Login → Discourse SSO → Strapi Backend → NextJS Frontend → Strapi Backend → Discourse Forum
```

1. User clicks "Community" button in header
2. Frontend gets Discourse login URL from backend
3. **User is redirected to Discourse login page**
4. **User clicks "Log In" on Discourse**
5. **Discourse initiates SSO by redirecting to our backend with nonce**
6. Backend validates signature and redirects to frontend login
7. User authenticates in our application (if not already authenticated)
8. Backend generates signed SSO response using Discourse-provided nonce
9. User is redirected back to Discourse with authenticated user data
10. Discourse validates signature and nonce, then logs user in

### Reverse SSO Flow (Discourse → App)
```
User → Discourse Forum → Strapi Backend → NextJS Frontend → Authentication → Strapi Backend → Discourse Forum
```

1. User visits Discourse directly and clicks "Log In"
2. Discourse redirects to your Strapi backend with SSO parameters
3. Backend validates signature and redirects to frontend login page
4. User authenticates in your application
5. Frontend sends authenticated request back to backend with original SSO parameters
6. Backend generates signed response and redirects user back to Discourse
7. User is logged into Discourse with their account data

### Detailed Sequence Diagrams

For comprehensive sequence diagrams showing the complete flow of both SSO directions, including all API calls, authentication steps, and error handling, see: **[SSO_SEQUENCE.md](./SSO_SEQUENCE.md)**

The sequence diagrams include:
- Forward SSO Flow with Redux-Saga integration
- Reverse SSO Flow with authentication handling
- Error handling scenarios
- All API endpoints and data exchanges

## Backend Setup (Strapi)

### 1. Dependencies
The following dependency has been added:
- `crypto-js`: For HMAC signature generation

### 2. API Structure
```
src/api/discourse/
├── controllers/discourse.ts    # API endpoints
├── routes/discourse.ts        # Route definitions
└── services/discourse-sso.ts  # SSO logic
```

### 3. Available Endpoints
- `GET /api/discourse/config` - Get Discourse configuration (public)
- `GET /api/discourse/sso` - **NEW**: Handle initial SSO request from Discourse (public)
- `POST /api/discourse/sso-url` - Generate SSO URL (authenticated)
- `GET /api/discourse/sso-url` - Generate SSO URL with query parameters (authenticated)
- `GET /api/discourse/sso-return` - Handle SSO return from Discourse
- `POST /api/discourse/sso-logout` - Handle SSO logout
- `POST /api/discourse/sync-user` - Sync user data with Discourse

### 4. Environment Variables
Add these to your `.env` file:
```env
# Discourse SSO Configuration
DISCOURSE_URL=https://your-discourse-forum.com
DISCOURSE_SSO_SECRET=your-shared-secret-key
DISCOURSE_COMMUNITY_NAME=Your Community Name
FRONTEND_URL=http://localhost:3000
```

## Frontend Setup (NextJS)

### 1. Architecture Components
- `src/components/Header/index.tsx`: Added Community button with Redux integration
- `src/pages/discourse/callback.tsx`: SSO return handler page
- `src/pages/api/discourse/index.ts`: API middleware for Discourse requests
- `src/features/discourse/`: Redux slice, saga, and selectors for state management
- `src/utils/request.ts`: Added Discourse API client methods

### 2. Redux Integration
The implementation follows the established Redux-Saga pattern:
- **Actions**: Defined in `discourse.slice.ts` for triggering SSO flow
- **Saga**: Handles async API calls and side effects in `discourse.saga.ts`
- **Selectors**: Provide access to Discourse state across components
- **API Middleware**: Routes requests through NextJS API layer for security

### 3. Environment Variables
Add these to your `.env.local` file:
```env
NEXT_PUBLIC_API_URL=http://localhost:1337
NEXT_PUBLIC_DISCOURSE_URL=https://your-discourse-forum.com
NEXT_PUBLIC_DISCOURSE_COMMUNITY_NAME=Your Community Name
```

### 4. Features
- **Redux-Saga Integration**: Follows established architecture patterns
- **Automatic Authentication**: Validates user state before SSO requests
- **Error Handling**: Comprehensive error handling through Redux state
- **Security**: Token management through established auth patterns
- **Responsive Design**: Community button integrates seamlessly with existing UI
- **Loading States**: Community button shows loading animation during SSO process
- **Consistent Styling**: Action buttons follow unified design patterns with hover effects and transitions
- **Accessibility**: Proper ARIA labels and disabled states during loading

## Discourse Configuration

### 1. Enable SSO in Discourse Admin
1. Go to Admin → Settings → Login
2. Enable "enable sso"
3. **IMPORTANT**: Set "sso url" to: `https://your-backend-domain.com/api/discourse/sso` (note: `/sso` not `/sso-url`)
4. Set "sso secret" to match your `DISCOURSE_SSO_SECRET`
5. Enable "sso overrides email", "sso overrides username", "sso overrides name" as needed
6. **Optional**: Enable "sso overrides avatar" and "sso overrides bio" for profile sync

### 2. Optional Settings
- Set "sso overrides avatar" if you want to sync user avatars
- Configure "sso overrides bio" for user bio synchronization
- Set up admin/moderator role mapping if needed

## Implementation Fix Based on Official Documentation

### The Problem
The original implementation incorrectly tried to initiate SSO from our application and generate nonces independently. This violated Discourse's SSO specification and caused "Account login timed out" errors.

### The Root Issue
**Discourse SSO must be initiated by Discourse itself**, not by external applications. According to the official documentation:

> "Clicking on login or avatar will, redirect you to `/session/sso` which in turn will redirect users to `discourse_connect_url` with a signed payload."

### The Correct Solution
The corrected implementation follows Discourse's official SSO specification:

1. **Forward Flow**: Users visit Discourse login page first, then Discourse initiates SSO
2. **Nonce Handling**: Only use nonces provided by Discourse in their SSO requests
3. **Response Format**: Generate proper SSO responses with user data using Discourse's nonce

### Key Changes Made
```typescript
// BEFORE (Incorrect): Trying to generate our own nonce
nonce = crypto.randomBytes(16).toString('hex');

// AFTER (Correct): Only respond to Discourse-initiated SSO
if (!sso || !sig) {
  throw new Error('SSO parameters are required. This method should only be called for reverse SSO flow.');
}
```

This ensures compliance with Discourse's official SSO implementation and prevents timeout errors.

## Redux Flow

### State Management
The Discourse integration uses Redux for state management:

```typescript
// Discourse state structure
interface DiscourseState {
  loading: boolean;
  error: string | null;
  ssoUrl: string | null;
  communityName: string | null;
  discourseUrl: string | null;
  ssoEnabled: boolean;
}
```

### Action Flow
1. **User clicks Community button** → `handleCommunityClick()`
2. **Component dispatches action** → `dispatch(discourseActions.getDiscourseSSOUrl())`
3. **Saga intercepts action** → `handleGetDiscourseSSOUrl()`
4. **Saga validates auth** → Checks `selectIsAuthenticated` and token
5. **Saga calls API** → `apiDiscourseRequest("get-sso-url", { token })`
6. **API middleware** → Routes to `/api/discourse` → Calls Strapi
7. **Success response** → `setDiscourseSSOUrl(ssoUrl)` + `window.open()`
8. **Error handling** → `setError(message)` for user feedback

## Testing the Integration

### 1. Start Both Services
```bash
# Backend (Strapi)
cd affiliate-cms
yarn dev

# Frontend (NextJS)
cd affliate-dashboard
yarn dev
```

### 2. Test Forward SSO Flow (App → Discourse)
1. Open frontend at `http://localhost:3000`
2. Sign in with your account
3. Look for the green "Community" button in the header
4. Click the Community button
5. You should be redirected to Discourse and automatically logged in

### 3. Test Reverse SSO Flow (Discourse → App)
1. Open Discourse forum directly (not through your app)
2. Click "Log In" button on Discourse
3. You should be redirected to your application's login page
4. Sign in with your credentials
5. After successful login, you should be automatically redirected back to Discourse
6. You should now be logged into Discourse with your account

### 3. Troubleshooting

#### Common Issues:

1. **"Account login timed out, please try logging in again"**
   - **Cause**: Invalid nonce or SSO payload format issues
   - **Root Cause**: Using self-generated nonce instead of Discourse-provided nonce
   - **Fix**: The backend now requests a valid nonce from Discourse before generating SSO payload
   - **Debug**: Check Strapi logs for nonce request/response details

2. **"Authentication required" error**
   - **Cause**: User not logged in or token expired
   - **Fix**: Ensure user is authenticated before accessing community

3. **"Invalid SSO signature" error**
   - **Cause**: Secret mismatch between systems
   - **Fix**: Verify `DISCOURSE_SSO_SECRET` is identical in both systems

4. **"Discourse is not configured" error**
   - **Cause**: Missing environment variables
   - **Fix**: Check all required environment variables are set

#### Debug Steps:

1. **Check SSO Payload Format**:
   ```bash
   # Decode the base64 payload to verify format
   echo "bm9uY2U9ZTllYzk1YTliZjcwYTkzMjFiMDMyOTk0OTZhOWEwNGMmZXh0ZXJuYWxfaWQ9MTE3MCZlbWFpbD1kYW5oMDIwNzE5OTglNDBnbWFpbC5jb20mdXNlcm5hbWU9ZGFuaDAyMDcxOTk4Jm5hbWU9ZGFuaDAyMDcxOTk4JmFkbWluPWZhbHNlJm1vZGVyYXRvcj1mYWxzZQ==" | base64 -d
   ```

2. **Verify HMAC Signature**:
   - Check that the signature generation uses the exact same secret
   - Ensure no extra encoding/decoding steps

3. **Check Strapi Logs**:
   ```bash
   # Look for SSO-related log messages
   tail -f affiliate-cms/logs/strapi.log | grep -i discourse
   ```

4. **Verify Discourse Configuration**:
   - Ensure SSO URL points to `/api/discourse/sso` (not `/sso-url`)
   - Check that the secret matches exactly
   - Verify all required SSO settings are enabled

5. **Test SSO Payload Manually**:
   - Use browser developer tools to inspect network requests
   - Verify the generated SSO URL format
   - Check for any URL encoding issues

### 4. Testing Checklist

#### Forward SSO (App → Discourse)
- [ ] Backend starts without errors
- [ ] Frontend starts without errors
- [ ] Redux store includes discourse reducer
- [ ] Community button appears for authenticated users
- [ ] Community button hidden for unauthenticated users
- [ ] Community button shows loading spinner when clicked
- [ ] Community button is disabled during loading state
- [ ] Button text changes to "Connecting..." during loading
- [ ] Clicking Community button dispatches Redux action
- [ ] Saga handles SSO URL generation correctly
- [ ] User is automatically logged into Discourse
- [ ] Error states are handled gracefully
- [ ] Loading state clears after completion or error

#### Reverse SSO (Discourse → App)
- [ ] Clicking "Log In" on Discourse redirects to your app
- [ ] SSO parameters are preserved during redirection
- [ ] Login page correctly processes SSO parameters
- [ ] After login, user is redirected back to Discourse
- [ ] User is automatically logged into Discourse
- [ ] User data is correctly synchronized with Discourse

## Security Considerations

1. **Secret Management**: Keep `DISCOURSE_SSO_SECRET` secure and identical in both systems
2. **HTTPS**: Use HTTPS in production for all SSO communications
3. **Token Validation**: Backend validates JWT tokens before generating SSO URLs
4. **Signature Verification**: Discourse validates HMAC signatures for all SSO requests

## User Data Mapping

The following user data is sent to Discourse:
- `external_id`: User ID from your system
- `email`: User email address
- `username`: User username (or email prefix if no username)
- `name`: User full name (or username/email if no full name)
- `avatar_url`: User avatar URL (optional)
- `admin`: Admin status (optional)
- `moderator`: Moderator status (optional)

## Maintenance

### Updating User Data
When user profiles are updated in your system, you can sync the changes with Discourse using:
```javascript
POST /api/discourse/sync-user
```

### Monitoring
Monitor the following for SSO health:
- SSO request success rates
- Authentication failures
- Discourse forum access patterns
- User synchronization status

## Support

For issues with this integration:
1. Check the troubleshooting section above
2. Review Strapi and NextJS logs
3. Verify Discourse admin settings
4. Test with a fresh user account
