#!/usr/bin/env node

/**
 * Debug script for Discourse SSO payload
 * Usage: node debug-sso.js <base64_payload> <secret>
 */

const crypto = require('crypto');

function debugSSOPayload(base64Payload, secret) {
  console.log('=== Discourse SSO Debug ===\n');
  
  // Decode the base64 payload
  console.log('1. Base64 Payload:');
  console.log(base64Payload);
  console.log();
  
  try {
    const decodedPayload = Buffer.from(base64Payload, 'base64').toString();
    console.log('2. Decoded Payload:');
    console.log(decodedPayload);
    console.log();
    
    // Parse the payload
    const params = new URLSearchParams(decodedPayload);
    console.log('3. Parsed Parameters:');
    for (const [key, value] of params.entries()) {
      console.log(`   ${key}: ${value}`);
    }
    console.log();
    
    // Generate expected signature
    if (secret) {
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(base64Payload)
        .digest('hex');
      
      console.log('4. Expected HMAC Signature:');
      console.log(expectedSignature);
      console.log();
      
      // Validate required fields
      console.log('5. Validation:');
      const requiredFields = ['nonce', 'external_id', 'email', 'username', 'name'];
      const missingFields = requiredFields.filter(field => !params.has(field));
      
      if (missingFields.length === 0) {
        console.log('   ✅ All required fields present');
      } else {
        console.log('   ❌ Missing required fields:', missingFields.join(', '));
      }
      
      // Check field formats
      const email = params.get('email');
      if (email && email.includes('@')) {
        console.log('   ✅ Email format looks valid');
      } else {
        console.log('   ❌ Email format invalid or missing');
      }
      
      const externalId = params.get('external_id');
      if (externalId && !isNaN(externalId)) {
        console.log('   ✅ External ID is numeric');
      } else {
        console.log('   ❌ External ID should be numeric');
      }
    }
    
  } catch (error) {
    console.error('Error decoding payload:', error.message);
  }
}

// Command line usage
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    console.log('Usage: node debug-sso.js <base64_payload> [secret]');
    console.log('');
    console.log('Example:');
    console.log('node debug-sso.js "bm9uY2U9ZTllYzk1YTliZjcwYTkzMjFiMDMyOTk0OTZhOWEwNGMmZXh0ZXJuYWxfaWQ9MTE3MCZlbWFpbD1kYW5oMDIwNzE5OTglNDBnbWFpbC5jb20mdXNlcm5hbWU9ZGFuaDAyMDcxOTk4Jm5hbWU9ZGFuaDAyMDcxOTk4JmFkbWluPWZhbHNlJm1vZGVyYXRvcj1mYWxzZQ==" "your-secret-key"');
    process.exit(1);
  }
  
  const [base64Payload, secret] = args;
  debugSSOPayload(base64Payload, secret);
}

module.exports = { debugSSOPayload };
