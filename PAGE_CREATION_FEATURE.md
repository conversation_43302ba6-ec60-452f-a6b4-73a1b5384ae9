# Page Creation and Editing Feature

## Overview

This document describes the comprehensive page creation and editing feature implemented for the affiliate dashboard. The feature allows users to create, edit, and manage rich text pages with a Notion-like editing experience.

## Architecture

### Backend (Strapi v5)
- **Content Type**: `api::page.page` with Yoopta Editor compatible JSON structure
- **Controllers**: Full CRUD operations with authentication and ownership validation
- **Services**: Content processing, auto-save, publishing, and utility functions
- **Routes**: Standard REST endpoints plus custom routes for auto-save and publishing

### Frontend (NextJS/TypeScript)
- **State Management**: Redux-Saga pattern with comprehensive error handling
- **Editor**: Custom rich text editor with Yoopta Editor compatible data format
- **Routing**: Dynamic routes for page editing (`/editor/[id]`) and preview (`/preview/[slug]`)
- **UI Components**: Consistent styling with loading states and animations

## Features Implemented

### 1. Page Creation (Two Entry Points)

#### Method A: Profile "Generate Page" Button
- Located in the Profile container (`/profile`)
- Creates a new page with default content
- Automatically navigates to the editor
- Shows loading states and error handling

#### Method B: AI Chat "/generate" Command
- Type `/generate` or `/generate [title]` in the AI chat
- Creates page with specified title or "Untitled Page"
- Returns formatted response with clickable editor link
- Supports help command (`/help` or `/commands`)

### 2. Rich Text Editor
- **Yoopta Editor Compatible**: Uses block-based JSON structure
- **Markdown Interface**: Simplified editing with markdown-like syntax
- **Auto-save**: Configurable auto-save with visual indicators
- **Toolbar**: Rich formatting options (bold, italic, headings, lists, etc.)
- **Preview Mode**: Toggle between edit and preview modes
- **Keyboard Shortcuts**: Ctrl+B (bold), Ctrl+I (italic), Ctrl+S (save)

### 3. Page Management
- **CRUD Operations**: Create, read, update, delete with proper authentication
- **Status Management**: Draft, published, archived states
- **Auto-save**: Background saving every 5 seconds
- **Version Tracking**: Last edited timestamps
- **Content Processing**: Automatic HTML and plain text generation for SEO

### 4. User Interface
- **Consistent Styling**: Matches existing dashboard design patterns
- **Loading States**: Spinners and disabled states during operations
- **Error Handling**: User-friendly error messages
- **Responsive Design**: Works on desktop and mobile devices
- **Navigation**: Seamless flow between profile, editor, and preview

## Data Structure

### Yoopta Editor Content Format
```typescript
interface YooptaBlock {
  id: string;
  type: string; // 'paragraph', 'heading-one', 'heading-two', etc.
  value: any[]; // Array of text objects
  meta?: {
    order: number;
    depth?: number;
    align?: 'left' | 'center' | 'right';
  };
}

interface YooptaContentValue {
  [blockId: string]: YooptaBlock;
}
```

### Page Schema
```typescript
interface IPage {
  id: number;
  documentId: string;
  title: string;
  slug: string;
  content?: YooptaContentValue; // Rich text content
  content_html?: string; // HTML for SEO
  content_plain?: string; // Plain text for search
  excerpt?: string;
  featured_image?: any;
  meta_title?: string;
  meta_description?: string;
  status: 'draft' | 'published' | 'archived';
  view_count: number;
  author: any;
  tags?: string[];
  last_edited_at?: string;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}
```

## API Endpoints

### Standard REST Endpoints
- `GET /api/pages` - List user's pages
- `POST /api/pages` - Create new page
- `GET /api/pages/:id` - Get specific page
- `PUT /api/pages/:id` - Update page
- `DELETE /api/pages/:id` - Delete page

### Custom Endpoints
- `POST /api/pages/auto-save/:id` - Auto-save page content
- `POST /api/pages/publish/:id` - Publish page
- `GET /api/pages/my-pages` - Get user's pages with filters

## Testing

### Manual Testing Steps

1. **Profile Page Creation**:
   - Navigate to `/profile`
   - Click "Generate Page" button
   - Verify navigation to editor
   - Test loading states

2. **AI Chat Page Creation**:
   - Open AI chat
   - Type `/generate Test Page`
   - Verify page creation response
   - Click editor link

3. **Page Editor**:
   - Test rich text editing
   - Verify auto-save functionality
   - Test save and publish buttons
   - Check preview mode

4. **Page Management**:
   - View pages in profile
   - Test edit and view buttons
   - Verify status indicators

### Test Page
- Access `/test-page-creation` for manual testing
- Test page creation and listing functionality

## File Structure

```
affiliate-cms/src/api/page/
├── content-types/page/schema.json
├── controllers/page.ts
├── services/page.ts
├── routes/page.ts
├── routes/custom-routes.ts
└── interfaces/page.interface.ts

affliate-dashboard/src/
├── features/page/
│   ├── page.slice.ts
│   └── page.saga.ts
├── pages/
│   ├── api/pages/
│   ├── editor/[id].tsx
│   ├── preview/[slug].tsx
│   └── test-page-creation.tsx
├── components/
│   └── RichTextEditor.tsx
└── containers/Profile/index.tsx
```

## Security Considerations

- **Authentication**: All endpoints require valid JWT tokens
- **Authorization**: Users can only access/edit their own pages
- **Input Validation**: Content is validated and sanitized
- **XSS Prevention**: HTML content is properly escaped in preview

## Performance Optimizations

- **Auto-save Debouncing**: Prevents excessive API calls
- **Content Processing**: HTML/plain text generated server-side
- **Pagination**: Page lists support pagination
- **Lazy Loading**: Editor components load on demand

## Future Enhancements

- **Collaborative Editing**: Real-time collaboration features
- **Advanced Media**: Image/video upload and management
- **Templates**: Pre-built page templates
- **SEO Tools**: Advanced SEO optimization features
- **Analytics**: Page view tracking and analytics
- **Export Options**: PDF, Word, and other format exports

## Troubleshooting

### Common Issues

1. **Page Not Saving**: Check authentication and network connectivity
2. **Editor Not Loading**: Verify component imports and dependencies
3. **Navigation Issues**: Check route configuration and permissions
4. **Content Not Displaying**: Verify data format and content processing

### Debug Tools

- Browser DevTools for frontend debugging
- Strapi admin panel for backend data inspection
- Redux DevTools for state management debugging
- Network tab for API request monitoring

## Support

For issues or questions about this feature:
1. Check the troubleshooting section above
2. Review the implementation files
3. Test with the provided test page
4. Verify authentication and permissions
