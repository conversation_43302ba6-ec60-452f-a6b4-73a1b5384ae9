const axios = require('axios');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://prod-alb-521497661.us-east-1.elb.amazonaws.com';
const AUTH_TOKEN =
  process.env.AUTH_TOKEN ||
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwiaWF0IjoxNzUxODgzNjQxLCJleHAiOjE3NTQ0NzU2NDF9.n-hg8l60KsEfHLw1RCA7g2SD4Mj06kAG1_wPVdjQrNg';

// Statistics tracking
const stats = {
  total: 0,
  processed: 0,
  success: 0,
  failed: 0,
  skipped: 0,
  errors: [],
};

/**
 * Fetch a single page of TikTok social listening records
 */
async function fetchTikTokRecordsPage(page, pageSize = 100) {
  try {
    console.log(`📄 Fetching page ${page}...`);

    const response = await axios.get(
      `${BASE_URL}/content-manager/collection-types/api::social-listening.social-listening`,
      {
        params: {
          page,
          pageSize,
          sort: 'video_id:ASC',
          'filters[$and][0][platform][$eq]': 'tiktok',
        },
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${AUTH_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );

    const { results, pagination } = response.data;
    console.log(`✅ Page ${page}: ${results.length} records fetched`);

    return { results, pagination };
  } catch (error) {
    console.error(`❌ Error fetching page ${page}:`, error.message);
    throw error;
  }
}

/**
 * Update thumbnail for a single record
 */
async function updateRecordThumbnail(record) {
  try {
    console.log(`🔄 Processing record ${record.id} (video_id: ${record.video_id})...`);

    // Check if record already has a thumbnail that looks like an S3 URL
    if (record.thumbnail && record.thumbnail.includes('amazonaws.com')) {
      console.log(`⏭️  Record ${record.id} already has S3 thumbnail, skipping...`);
      stats.skipped++;
      return { success: true, skipped: true };
    }

    // Call the TikTok cover processing endpoint
    const response = await axios.get(
      `${BASE_URL}/api/social-listenings/tiktok-detail-cover/${record.video_id}`,
      {
        headers: {
          Accept: 'application/json',
        },
        timeout: 30000, // 30 second timeout
      }
    );

    if (response.data && response.data.success) {
      const { s3Url, s3FileKey, s3Bucket } = response.data.data;

      console.log(`✅ Record ${record.id} updated successfully`);
      console.log(`   📸 New thumbnail: ${s3Url}`);
      console.log(`   🗂️  S3 Key: ${s3FileKey}`);

      stats.success++;
      return {
        success: true,
        s3Url,
        s3FileKey,
        s3Bucket,
      };
    } else {
      throw new Error('API response indicates failure');
    }
  } catch (error) {
    console.error(`❌ Failed to update record ${record.id}:`, error.message);
    stats.failed++;
    stats.errors.push({
      recordId: record.id,
      videoId: record.video_id,
      error: error.message,
    });
    return { success: false, error: error.message };
  }
}

/**
 * Process records in batches with delay
 */
async function processRecordsInBatches(
  records,
  batchSize = 5,
  delayMs = 2000,
  currentPage = 1,
  totalPages = 1
) {
  console.log(
    `🚀 Processing ${records.length} records from page ${currentPage}/${totalPages} in batches of ${batchSize}...`
  );

  for (let i = 0; i < records.length; i += batchSize) {
    const batch = records.slice(i, i + batchSize);
    const batchNumber = Math.floor(i / batchSize) + 1;
    const totalBatches = Math.ceil(records.length / batchSize);

    console.log(
      `\n📦 Page ${currentPage}/${totalPages} - Processing batch ${batchNumber}/${totalBatches} (records ${i + 1}-${Math.min(i + batchSize, records.length)})...`
    );

    // Process batch in parallel
    const promises = batch.map((record) => updateRecordThumbnail(record));
    await Promise.all(promises);

    stats.processed += batch.length;

    // Progress update
    const progressPercent =
      stats.total > 0 ? ((stats.processed / stats.total) * 100).toFixed(1) : 0;
    console.log(`📈 Progress: ${stats.processed}/${stats.total} (${progressPercent}%)`);
    console.log(`📊 Success: ${stats.success}, Failed: ${stats.failed}, Skipped: ${stats.skipped}`);

    // Delay between batches (except for the last batch of the page)
    if (i + batchSize < records.length) {
      console.log(`⏳ Waiting ${delayMs}ms before next batch...`);
      await new Promise((resolve) => setTimeout(resolve, delayMs));
    }
  }
}

/**
 * Print final statistics
 */
function printFinalStats() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 FINAL STATISTICS');
  console.log('='.repeat(60));
  console.log(`📝 Total records: ${stats.total}`);
  console.log(`✅ Successfully updated: ${stats.success}`);
  console.log(`❌ Failed: ${stats.failed}`);
  console.log(`⏭️  Skipped (already had S3 thumbnail): ${stats.skipped}`);
  console.log(`🔄 Processed: ${stats.processed}`);

  if (stats.failed > 0) {
    console.log('\n❌ FAILED RECORDS:');
    stats.errors.forEach((error, index) => {
      console.log(`${index + 1}. Record ID: ${error.recordId}, Video ID: ${error.videoId}`);
      console.log(`   Error: ${error.error}`);
    });
  }

  const successRate = stats.total > 0 ? ((stats.success / stats.total) * 100).toFixed(1) : 0;
  console.log(`\n🎯 Success rate: ${successRate}%`);
  console.log('='.repeat(60));
}

/**
 * Main function to update all thumbnails page by page
 */
async function updateAllThumbnails() {
  const startTime = Date.now();

  try {
    console.log('🚀 Starting TikTok thumbnail update process...');
    console.log(`🌐 Base URL: ${BASE_URL}`);
    console.log(`🔑 Using auth token: ${AUTH_TOKEN.substring(0, 20)}...`);

    let currentPage = 1;
    let totalPages = 1;
    const pageSize = 100;

    // Get first page to determine total count
    console.log('🔍 Getting initial page to determine total records...');
    const firstPageData = await fetchTikTokRecordsPage(currentPage, pageSize);

    if (!firstPageData.results || firstPageData.results.length === 0) {
      console.log('ℹ️  No TikTok records found to process.');
      return;
    }

    stats.total = firstPageData.pagination.total;
    totalPages = firstPageData.pagination.pageCount;

    console.log(`📊 Found ${stats.total} total TikTok records across ${totalPages} pages`);
    console.log(`📄 Processing ${pageSize} records per page, 5 records per batch\n`);

    // Process first page
    await processRecordsInBatches(firstPageData.results, 5, 2000, currentPage, totalPages);
    currentPage++;

    // Process remaining pages
    while (currentPage <= totalPages) {
      try {
        console.log(`\n🔄 Moving to page ${currentPage}/${totalPages}...`);

        // Small delay between pages
        await new Promise((resolve) => setTimeout(resolve, 1000));

        const pageData = await fetchTikTokRecordsPage(currentPage, pageSize);

        if (pageData.results && pageData.results.length > 0) {
          await processRecordsInBatches(pageData.results, 5, 2000, currentPage, totalPages);
        }

        currentPage++;
      } catch (error) {
        console.error(`❌ Error processing page ${currentPage}:`, error.message);
        console.log('⏭️  Continuing with next page...');
        currentPage++;
      }
    }

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000 / 60).toFixed(2);

    console.log(`\n⏱️  Total processing time: ${duration} minutes`);
    printFinalStats();
  } catch (error) {
    console.error('💥 Fatal error during processing:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

/**
 * Handle command line arguments
 */
function parseArguments() {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
📖 TikTok Thumbnail Update Script

Usage: node update-thumbnail.js [options]

Options:
  --help, -h          Show this help message
  --dry-run          Show what would be processed without making changes
  --batch-size=N     Number of records to process in parallel (default: 5)
  --delay=N          Delay between batches in milliseconds (default: 2000)

Environment Variables:
  BASE_URL           Base URL of the Strapi instance
  AUTH_TOKEN         Authorization token for API access

Examples:
  node update-thumbnail.js
  node update-thumbnail.js --batch-size=3 --delay=3000
  node update-thumbnail.js --dry-run
`);
    process.exit(0);
  }

  if (args.includes('--dry-run')) {
    console.log('🔍 DRY RUN MODE - No actual updates will be made');
    // You could implement dry run logic here
  }
}

// Run the script
if (require.main === module) {
  parseArguments();
  updateAllThumbnails().catch((error) => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = {
  updateAllThumbnails,
  fetchTikTokRecordsPage,
  updateRecordThumbnail,
};
