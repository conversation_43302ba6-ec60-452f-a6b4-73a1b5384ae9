# TikTok Video Cover Image Processing Implementation

This document describes the implementation of TikTok video detail fetching with automatic cover image download and Google Drive upload functionality.

## Overview

The implementation provides a complete solution for:
1. Fetching TikTok video details using the TikTok API
2. Downloading the cover image from the TikTok response
3. Uploading the image to Google Drive
4. Generating a public link for the uploaded image
5. Updating the database with the new public link

## API Endpoint

### GET `/api/social-listenings/tiktok-detail-cover/:videoId`

Fetches TikTok video details, downloads the cover image, uploads it to Google Drive, and returns the public link.

#### Parameters
- `videoId` (string, required): The TikTok video ID

#### Response Format
```json
{
  "success": true,
  "data": {
    "videoId": "6845220806658624773",
    "originalThumbnail": "https://p19-sign.tiktokcdn-us.com/...",
    "googleDriveLink": "https://drive.google.com/uc?id=FILE_ID",
    "googleDriveWebViewLink": "https://drive.google.com/file/d/FILE_ID/view",
    "fileId": "GOOGLE_DRIVE_FILE_ID",
    "fileName": "tiktok_cover_6845220806658624773.jpg",
    "videoDetail": {
      "success": true,
      "thumbnail": "https://p19-sign.tiktokcdn-us.com/..."
    }
  }
}
```

#### Error Response
```json
{
  "success": false,
  "error": "Error message describing what went wrong"
}
```

## Implementation Details

### Files Created/Modified

1. **`src/utils/google-drive.ts`** - Google Drive service for file upload and management
2. **`src/utils/file-download.ts`** - Utility for downloading files from URLs
3. **`src/api/social-listening/services/social-listening.ts`** - Enhanced with `getTiktokVideoDetailWithCover` method
4. **`src/api/social-listening/controllers/social-listening.ts`** - Added controller endpoint
5. **`src/api/social-listening/routes/custom-api.ts`** - Added route configuration

### Dependencies Added

- `googleapis` - Google Drive API client
- `axios` - HTTP client for file downloads (already existed)

### Environment Variables Required

Add these to your `.env` file:

```env
# Google Drive Configuration
GOOGLE_DRIVE_TYPE=service_account
GOOGLE_DRIVE_PROJECT_ID=your-project-id
GOOGLE_DRIVE_PRIVATE_KEY_ID=your-private-key-id
GOOGLE_DRIVE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
GOOGLE_DRIVE_CLIENT_EMAIL=<EMAIL>
GOOGLE_DRIVE_CLIENT_ID=your-client-id
GOOGLE_DRIVE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
GOOGLE_DRIVE_TOKEN_URI=https://oauth2.googleapis.com/token
GOOGLE_DRIVE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
GOOGLE_DRIVE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com
GOOGLE_DRIVE_TIKTOK_FOLDER_ID=optional-folder-id-for-tiktok-covers
```

## Google Drive Setup

### 1. Create a Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one

### 2. Enable Google Drive API
1. Go to "APIs & Services" > "Library"
2. Search for "Google Drive API"
3. Click "Enable"

### 3. Create Service Account
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "Service Account"
3. Fill in the service account details
4. Grant the service account the "Editor" role
5. Create and download the JSON key file

### 4. Configure Environment Variables
Extract the values from the downloaded JSON file and add them to your `.env` file.

### 5. Share Drive Folder (Optional)
If you want to upload to a specific folder:
1. Create a folder in Google Drive
2. Share the folder with your service account email
3. Copy the folder ID from the URL and set it as `GOOGLE_DRIVE_TIKTOK_FOLDER_ID`

## Testing

### Manual Testing
Use the provided test script:

```bash
node test-tiktok-cover.js [videoId]
```

Example:
```bash
node test-tiktok-cover.js 6845220806658624773
```

### API Testing
Use curl or any HTTP client:

```bash
curl "http://localhost:1337/api/social-listenings/tiktok-detail-cover/6845220806658624773"
```

## Flow Diagram

```
1. Client Request
   ↓
2. Controller validates videoId
   ↓
3. Service calls TikTok API for video details
   ↓
4. Extract thumbnail URL from response
   ↓
5. Download image to temporary location
   ↓
6. Upload image to Google Drive
   ↓
7. Set public permissions on uploaded file
   ↓
8. Generate public link
   ↓
9. Update database record (if exists)
   ↓
10. Clean up temporary file
   ↓
11. Return response with public link
```

## Error Handling

The implementation includes comprehensive error handling for:
- Invalid video IDs
- TikTok API failures
- Image download failures
- Google Drive upload failures
- Network timeouts
- File system errors

## Security Considerations

- Service account credentials should be kept secure
- Temporary files are automatically cleaned up
- Public links are generated with appropriate permissions
- Rate limiting should be considered for production use

## Performance Notes

- Images are downloaded to a temporary directory and cleaned up immediately
- Google Drive uploads are performed asynchronously
- Database updates are optional and won't fail the main operation
- The process typically takes 5-15 seconds depending on image size and network conditions
