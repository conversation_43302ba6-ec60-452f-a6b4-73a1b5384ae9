/**
 * Global configuration utility functions
 */

/**
 * Get the global configuration from Strapi.
 * @returns The global configuration object
 */
export async function getGlobalConfig() {
  const strapi = global.strapi;
  const globalConfig = await strapi.entityService.findMany('api::global.global');

  if (!globalConfig || (Array.isArray(globalConfig) && globalConfig.length === 0)) {
    throw new Error('Global configuration not found');
  }

  // Handle whether globalConfig is an array or a single object
  return Array.isArray(globalConfig) ? globalConfig[0] : globalConfig;
}

/**
 * Get a specific property from the global configuration
 * @param propertyName The name of the property to retrieve
 * @param defaultValue Optional default value to return if property is not found
 * @returns The value of the specified property or the default value
 */
export async function getGlobalConfigProperty<T>(
  propertyName: string,
  defaultValue?: T
): Promise<T> {
  try {
    const config = await getGlobalConfig();

    if (config && propertyName in config && config[propertyName]) {
      return config[propertyName] as T;
    }

    if (defaultValue !== undefined) {
      return defaultValue;
    }

    throw new Error(`Global configuration property "${propertyName}" not found`);
  } catch (error) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw error;
  }
}
