/**
 * affiliate service
 */

import { factories } from '@strapi/strapi';
import { IAffiliate } from '../interfaces';

export default factories.createCoreService('api::affiliate.affiliate', ({ strapi }) => ({
  // Keep the original service functions

  // Update findBySlug to search directly by slug field
  async findBySlug(slug: string, populate?: any) {
    const results = await strapi.entityService.findMany('api::affiliate.affiliate', {
      filters: { slug },
      populate: populate || ['*'],
    });

    // Fallback to name search if no results found by slug
    if (results.length === 0) {
      const nameResults = await strapi.entityService.findMany('api::affiliate.affiliate', {
        filters: { name: slug },
        populate: populate || ['*'],
      });

      return nameResults.length > 0 ? nameResults[0] : null;
    }

    return results.length > 0 ? results[0] : null;
  },

  // Helper function to convert rich text blocks to plain text
  convertRichTextToPlainText(richTextContent) {
    if (!richTextContent || !Array.isArray(richTextContent)) {
      return '';
    }

    return richTextContent
      .map((block) => {
        // Handle paragraph blocks
        if (block.type === 'paragraph' && Array.isArray(block.children)) {
          return block.children
            .map((child) => {
              // Extract text from child objects
              if (typeof child === 'object' && child !== null) {
                return child.text || '';
              }
              return child || '';
            })
            .join('');
        }

        // Handle list blocks
        if ((block.type === 'ul' || block.type === 'ol') && Array.isArray(block.children)) {
          return block.children
            .map((item, index) => {
              const prefix = block.type === 'ol' ? `${index + 1}. ` : '• ';
              if (item.type === 'li' && Array.isArray(item.children)) {
                const itemText = item.children.map((child) => child.text || '').join('');
                return `${prefix}${itemText}`;
              }
              return '';
            })
            .join('\n');
        }

        // Handle headers
        if (block.type && block.type.startsWith('heading') && Array.isArray(block.children)) {
          return block.children.map((child) => child.text || '').join('');
        }

        return '';
      })
      .join('\n')
      .trim();
  },

  async generateAffiliateSummary(affiliate: IAffiliate, userId: number) {
    try {
      // Get or create a session for this summary
      const session = await strapi.entityService.create('api::aiscript-session.aiscript-session', {
        data: {
          session_id: `summary-${affiliate.slug || affiliate.name}-${new Date().toISOString()}`,
          session_status: 'active',
          start_time: new Date(),
          users_permissions_user: userId,
        },
      });

      let promptContent = '';

      const summaryPrompt = await strapi.service('api::prompt.prompt').getSummaryPrompt();
      if (summaryPrompt) {
        promptContent = summaryPrompt.content;
      }

      promptContent += 'INPUT: \n';

      // program detail
      promptContent += `- Detail: ${this.convertRichTextToPlainText(affiliate.detail)} \n`;
      // cookies
      if (affiliate.cookies_duration) {
        promptContent += '- Cookies Duration: ';
        promptContent += affiliate.cookies_duration;
      }

      // Average conversion value
      if (affiliate.avg_conversion) {
        promptContent += '- Average conversion value: \n';
        promptContent += affiliate.avg_conversion;
      }

      // Convert rich text commission detail to plain text
      const commissionDetailText = this.convertRichTextToPlainText(affiliate.commission_detail);
      promptContent += `- Commission structure: ${commissionDetailText}\n`;

      if (affiliate.commission) {
        const { value_from, value_to, avg_commission } = affiliate.commission;
        promptContent += `+ Commission Rate: ${avg_commission}, from: ${value_from}, to: ${value_to}`;
      }

      if (affiliate.payment_methods) {
        promptContent += '- Payment methods: ';
        promptContent += affiliate.payment_methods.map((method) => method.name).join(', ');
      }

      if (affiliate.traffic_webs) {
        promptContent += '- Traffic statistics: \n';
        promptContent += affiliate.traffic_webs
          .map((traffic) => {
            const {
              year,
              month,
              time_on_site,
              visits,
              page_per_visit,
              traffic_sources,
              top_countries,
            } = traffic;
            return `Month: ${month} - Year: ${year} \n 
                    Time on site: ${time_on_site} \n
                    Visits: ${visits} \n
                    Page per visit: ${page_per_visit} \n
                    Traffic sources: ${JSON.stringify(traffic_sources)} \n
                    Key Geographic regions: ${JSON.stringify(top_countries)} \n
            `;
          })
          .join(', ');
      }

      // Send to AI service for processing using the proper session
      const aiService = strapi.service('api::aiscript.aiscript');
      const summary = await aiService.processUserScript({
        message: promptContent,
        session,
        isUseDefaultPrompt: false,
      });

      const prompts = await strapi.entityService.findMany('api::prompt.prompt', {
        filters: { type: 'summary' },
      });

      return {
        summary,
        suggestions: prompts,
        affiliate_id: affiliate.id,
        session_id: session.session_id,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error generating affiliate summary:', error);
      throw error;
    }
  },

  /**
   * Bulk update recurring_priority for all published affiliates and republish them
   */
  async bulkUpdateRecurringPriority() {
    try {
      console.log('Starting bulk update of recurring_priority for published affiliates');

      // Get all published affiliates using Document Service API
      const publishedAffiliates = await strapi.documents('api::affiliate.affiliate').findMany({
        status: 'published', // Only get published documents
        fields: ['id', 'name', 'recurring', 'recurring_priority'],
        pagination: {
          limit: -1, // Get all records
        },
      });

      console.log(`Found ${publishedAffiliates.length} published affiliates to update`);

      if (publishedAffiliates.length === 0) {
        return {
          success: true,
          message: 'No published affiliates found to update',
          updated: 0,
          errors: [],
        };
      }

      // Import the calculateRecurringPriority function
      const { calculateRecurringPriority } = await import('../content-types/affiliate/lifecycles');

      const batchSize = 50; // Process in batches to avoid overwhelming the database
      const totalBatches = Math.ceil(publishedAffiliates.length / batchSize);
      let totalUpdated = 0;
      const errors = [];

      console.log(`Processing ${publishedAffiliates.length} affiliates in ${totalBatches} batches`);

      // Process affiliates in batches
      for (let i = 0; i < publishedAffiliates.length; i += batchSize) {
        const batchStartTime = Date.now();
        const currentBatch = publishedAffiliates.slice(i, i + batchSize);
        const batchNum = Math.floor(i / batchSize) + 1;

        console.log(
          `Processing batch ${batchNum}/${totalBatches} with ${currentBatch.length} affiliates`
        );

        // Process each affiliate in the current batch
        const batchPromises = currentBatch.map(async (affiliate) => {
          try {
            // Calculate new recurring_priority
            const dataWithPriority = await calculateRecurringPriority({
              recurring: affiliate.recurring,
            });

            const newPriority = dataWithPriority.recurring_priority;

            console.log('new priority', newPriority);
            console.log('affiliate.recurring_priority', affiliate.recurring_priority);

            // Debug: Check both draft and published versions before update
            const draftVersion = await strapi.documents('api::affiliate.affiliate').findOne({
              documentId: affiliate.documentId,
              status: 'draft',
            });
            const publishedVersion = await strapi.documents('api::affiliate.affiliate').findOne({
              documentId: affiliate.documentId,
              status: 'published',
            });

            console.log(
              `Before update - Draft priority: ${draftVersion?.recurring_priority}, Published priority: ${publishedVersion?.recurring_priority}`
            );

            // Only update if the priority has changed
            if (newPriority !== affiliate.recurring_priority) {
              console.log(
                `Updating affiliate ${affiliate.name} (ID: ${affiliate.id}) - Priority: ${affiliate.recurring_priority} -> ${newPriority}`
              );

              // Since the lifecycle hooks seem to work correctly during publish,
              // let's update the draft with the recurring field to trigger the lifecycle hook
              // and then publish to get the calculated priority
              const draftResult = await strapi.documents('api::affiliate.affiliate').update({
                documentId: affiliate.documentId,
                data: {
                  recurring: affiliate.recurring, // This will trigger the lifecycle hook to calculate priority
                },
              });

              console.log('Draft update result (should trigger lifecycle):', draftResult);
              console.log(
                'Draft recurring_priority after lifecycle:',
                draftResult.recurring_priority
              );

              // Now publish the changes - this should have the correct priority
              const publishResult = await strapi.documents('api::affiliate.affiliate').publish({
                documentId: affiliate.documentId,
              });

              console.log('Publish result:', publishResult);

              // Verify the published version was updated
              const verifyPublished = await strapi.documents('api::affiliate.affiliate').findOne({
                documentId: affiliate.documentId,
                status: 'published',
              });

              console.log(
                `Verification - Published version recurring_priority: ${verifyPublished?.recurring_priority}`
              );

              if (verifyPublished?.recurring_priority !== newPriority) {
                console.error(
                  `WARNING: Published version still has old priority ${verifyPublished?.recurring_priority}, expected ${newPriority}`
                );
              }

              return { success: true, id: affiliate.id, name: affiliate.name };
            } else {
              console.log(
                `Affiliate ${affiliate.name} (ID: ${affiliate.id}) already has correct priority: ${newPriority}`
              );
              return { success: true, id: affiliate.id, name: affiliate.name, skipped: true };
            }
          } catch (error) {
            console.error(`Error updating affiliate ${affiliate.id}:`, error);
            return {
              success: false,
              id: affiliate.id,
              name: affiliate.name,
              error: error.message,
            };
          }
        });

        // Wait for all affiliates in the current batch to complete
        const batchResults = await Promise.all(batchPromises);

        // Count successful updates in this batch
        const batchUpdated = batchResults.filter(
          (result) => result.success && !result.skipped
        ).length;
        const batchErrors = batchResults.filter((result) => !result.success);

        totalUpdated += batchUpdated;
        errors.push(...batchErrors);

        const batchTime = Date.now() - batchStartTime;
        console.log(
          `Batch ${batchNum} completed in ${batchTime}ms - Updated: ${batchUpdated}, Errors: ${batchErrors.length}`
        );

        // Add a small delay between batches to prevent overwhelming the database
        if (i + batchSize < publishedAffiliates.length) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      console.log(
        `Bulk update completed - Total updated: ${totalUpdated}, Total errors: ${errors.length}`
      );

      return {
        success: true,
        message: `Successfully updated ${totalUpdated} affiliates`,
        total: publishedAffiliates.length,
        updated: totalUpdated,
        errors: errors,
      };
    } catch (error) {
      console.error('Error in bulk update recurring priority:', error);
      throw error;
    }
  },
}));
