/**
 * chat-message controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::chat-message.chat-message', ({ strapi }) => ({
  async sendMessage(ctx) {
    try {
      const { message } = ctx.request.body;

      // Validate user token
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized('Invalid or expired authorization token');
      }

      if (!message) {
        return ctx.badRequest('Message content is required');
      }

      // Get or create active session
      let session = await strapi
        .service('api::chat-session.chat-session')
        .getActiveSession(user.id);
      if (!session) {
        session = await strapi.service('api::chat-session.chat-session').createSession(user.id);
      }

      // Check if session is active
      if (session.session_status !== 'active') {
        return ctx.badRequest('Cannot send message to an ended session');
      }

      // Store user message
      const userMessage = await strapi
        .service('api::chat-message.chat-message')
        .createMessage(message, session.id, 'incoming');

      // Send message to chatbot
      const chatbotResponse = await strapi
        .service('api::chat-message.chat-message')
        .sendToChatbot(message, session.session_id);

      // Store chatbot response
      const botMessage = await strapi
        .service('api::chat-message.chat-message')
        .createMessage(chatbotResponse, session.id, 'outgoing');

      return {
        data: {
          userMessage,
          botMessage,
          sessionId: session.session_id,
        },
      };
    } catch (err) {
      console.error('Error in sendMessage controller:', err);
      return ctx.badRequest('Failed to process message', { error: err });
    }
  },
}));
