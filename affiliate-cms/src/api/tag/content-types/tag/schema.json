{"kind": "collectionType", "collectionName": "tags", "info": {"singularName": "tag", "pluralName": "tags", "displayName": "Tag", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string"}, "icon": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "affiliates": {"type": "relation", "relation": "manyToMany", "target": "api::affiliate.affiliate", "inversedBy": "tags"}}}