/**
 * referrer-link controller
 */

import { factories } from '@strapi/strapi';
import { errors } from '@strapi/utils';
const { ApplicationError, NotFoundError } = errors;

// Utility function to generate a unique short link
const generateShortLink = async () => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let shortLink = '';
  let isUnique = false;

  while (!isUnique) {
    shortLink = '';
    for (let i = 0; i < 8; i++) {
      shortLink += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    // Check if this short link already exists
    const existingLink = await strapi.entityService.findMany('api::referrer-link.referrer-link', {
      filters: { short_link: shortLink },
      limit: 1,
    });

    if (!existingLink || existingLink.length === 0) {
      isUnique = true;
    }
  }

  return shortLink;
};

const processUrl = async (data) => {
  // Skip if there's no URL
  if (!data.url) return data;

  try {
    // Parse the URL
    const url = new URL(data.url);
    console.log('Processing URL:', url.toString());

    // Check if URL already has a via parameter
    if (url.searchParams.has('via')) return data;

    // Remove trailing slash from pathname
    if (url.pathname.endsWith('/') && url.pathname.length > 1) {
      url.pathname = url.pathname.slice(0, -1);
    }

    // Get the associated referrer
    if (data.referrer) {
      // Handle different formats of referrer data
      let referrerId;

      if (typeof data.referrer === 'object' && data.referrer.connect) {
        // Handle connect format from Strapi v4
        referrerId = data.referrer.connect[0].id;
      } else if (typeof data.referrer === 'number' || typeof data.referrer === 'string') {
        // Handle direct ID assignment
        referrerId = data.referrer;
      }

      if (referrerId) {
        const referrer = await strapi.entityService.findOne('api::referrer.referrer', referrerId, {
          fields: ['referral_code'],
        });

        if (referrer && referrer.referral_code) {
          // Add the via parameter
          url.searchParams.set('via', referrer.referral_code);
          data.url = url.toString();
        }
      }
    }
  } catch (error) {
    // Log the error but don't fail the save operation
    console.error('Error processing URL in referrer-link:', error);
  }

  return data;
};
export default factories.createCoreController('api::referrer-link.referrer-link', ({ strapi }) => ({
  async find(ctx) {
    // Check authentication
    const { user } = ctx.state;
    if (!user) {
      return ctx.unauthorized('You must be logged in to access this resource');
    }

    // First, find the referrer associated with this user
    const referrers = await strapi.entityService.findMany('api::referrer.referrer', {
      filters: { user: user.id },
    });

    if (!referrers || referrers.length === 0) {
      return [];
    }

    const referrerId = referrers[0].documentId;
    console.log('Using referrerId:', referrerId, 'with type:', typeof referrerId);

    // Use Strapi's query format for relation filtering
    ctx.query = {
      ...ctx.query,
      filters: {
        ...(typeof ctx.query.filters === 'object' && ctx.query.filters !== null
          ? ctx.query.filters
          : {}),
        referrer: {
          documentId: referrerId,
        },
      },
      populate: {
        page: {
          fields: ['id', 'documentId', 'title', 'slug'],
        },
      },
    };

    console.log('Query after adding referrer filter:', JSON.stringify(ctx.query, null, 2));

    try {
      // Call the parent controller's find method
      const { data, meta } = await super.find(ctx);
      console.log('Query results count:', data?.length);
      return { data, meta };
    } catch (err) {
      console.error('Error in find method:', err);
      ctx.throw(500, err);
    }
  },

  async create(ctx) {
    const { user } = ctx.state;

    if (!user) {
      return ctx.unauthorized('You must be logged in to create a referrer link');
    }

    try {
      const { url, name, short_link, page_id } = ctx.request.body.data || ctx.request.body;
      console.log('Creating referrer link for user:', user.id, 'with URL:', url, 'name:', name, 'and page_id:', page_id);

      if (!url) {
        return ctx.badRequest('URL is required');
      }

      // Validate URL format
      try {
        new URL(url);
      } catch (urlError) {
        return ctx.badRequest('Invalid URL format');
      }

      const referrers = await strapi.entityService.findMany('api::referrer.referrer', {
        filters: { user: user.id },
      });

      if (!referrers || referrers.length === 0) {
        return ctx.badRequest(
          'No referrer found for this user. Please create a referrer profile first.'
        );
      }

      const referrerId = referrers[0].id;

      // Check if URL already exists for this user
      const existingUrlLink = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          filters: {
            url,
            user: user.id,
          },
          limit: 1,
        }
      );

      if (existingUrlLink && existingUrlLink.length > 0) {
        return ctx.badRequest('A referrer link with this URL already exists for your account');
      }

      // Check if short_link already exists (if provided)
      if (short_link) {
        const existingShortLink = await strapi.entityService.findMany(
          'api::referrer-link.referrer-link',
          {
            filters: { short_link },
            limit: 1,
          }
        );

        if (existingShortLink && existingShortLink.length > 0) {
          return ctx.badRequest('This short link is already taken. Please choose a different one.');
        }
      }

      // Validate page_id if provided
      if (page_id) {
        // Check if the page exists and belongs to the user
        const page = await strapi.documents('api::page.page').findOne({
          documentId: page_id,
          filters: {
            author: user.id,
            status: 'published'
          }
        });

        if (!page) {
          return ctx.badRequest('Page not found or not accessible');
        }

        // Check if the page is already linked to another referrer-link
        const existingPageLink = await strapi.entityService.findMany(
          'api::referrer-link.referrer-link',
          {
            filters: {
              page: {
                id: page.id
              }
            },
            limit: 1,
          }
        );

        if (existingPageLink && existingPageLink.length > 0) {
          return ctx.badRequest('This page is already linked to another referrer-link');
        }
      }

      // Process the URL before saving
      const processedData = await processUrl({
        url,
        name: name || url,
        referrer: referrerId,
      });

      // Prepare data for creation
      const createData = {
        ...processedData,
        visitors: 0,
        leads: 0,
        conversions: 0,
        user: user.id,
      };

      // Only add short_link if it's provided and not empty
      if (short_link && short_link.trim()) {
        createData.short_link = short_link.trim();
      }

      // Add page relationship if page_id is provided
      if (page_id) {
        const page = await strapi.documents('api::page.page').findOne({
          documentId: page_id
        });
        if (page) {
          createData.page = page.id;
        }
      }

      // Initialize view tracking fields (visitors is handled by schema default)
      createData.direct_page_views = 0;
      createData.referrer_link_views = 0;
      createData.short_link_views = 0;
      createData.referrer_sources = {};

      // Create the referrer link directly with entityService
      const newLink = await strapi.entityService.create('api::referrer-link.referrer-link', {
        data: createData,
      });

      return this.transformResponse(newLink);
    } catch (err) {
      console.error('Error creating referrer link:', err);

      // Handle specific validation errors
      if (err.name === 'YupValidationError' || err.message?.includes('YupValidationError')) {
        const errorDetails = err.details?.errors || [];
        const errorMessages = errorDetails.map((error) => error.message || error).join(', ');
        return ctx.badRequest(`Validation error: ${errorMessages || 'Invalid data provided'}`);
      }

      // Handle unique constraint violations
      if (err.message?.includes('unique constraint') || err.code === 'ER_DUP_ENTRY') {
        if (err.message?.includes('url')) {
          return ctx.badRequest('A referrer link with this URL already exists');
        }
        if (err.message?.includes('short_link')) {
          return ctx.badRequest('This short link is already taken');
        }
        return ctx.badRequest('A referrer link with this data already exists');
      }

      ctx.throw(500, 'Failed to create referrer link');
    }
  },

  async update(ctx) {
    try {
      const { user } = ctx.state;

      if (!user) {
        return ctx.unauthorized('You must be logged in to update a referrer link');
      }

      // Get the data from the request body
      let data = ctx.request.body.data || ctx.request.body;

      // Validate URL format if URL is being updated
      if (data.url) {
        try {
          new URL(data.url);
        } catch (urlError) {
          return ctx.badRequest('Invalid URL format');
        }
      }

      // get referrer by user using entity service
      const referrers = await strapi.entityService.findMany('api::referrer.referrer', {
        filters: { user: user.id },
      });

      if (!referrers || referrers.length === 0) {
        throw new NotFoundError('No referrer found for this user');
      }

      // Add the referrer to the data for processUrl
      data.referrer = referrers[0].id;

      // Process the URL before updating
      data = await processUrl(data);

      console.log('Updating referrer link with data:', data);
      // Update the referrer link using entityService
      ctx.request.body.data = data;

      return super.update(ctx);
    } catch (err) {
      console.error('Error updating referrer link:', err);

      // Handle specific validation errors
      if (err.name === 'YupValidationError' || err.message?.includes('YupValidationError')) {
        const errorDetails = err.details?.errors || [];
        const errorMessages = errorDetails.map((error) => error.message || error).join(', ');
        return ctx.badRequest(`Validation error: ${errorMessages || 'Invalid data provided'}`);
      }

      // Handle unique constraint violations
      if (err.message?.includes('unique constraint') || err.code === 'ER_DUP_ENTRY') {
        if (err.message?.includes('url')) {
          return ctx.badRequest('A referrer link with this URL already exists');
        }
        if (err.message?.includes('short_link')) {
          return ctx.badRequest('This short link is already taken');
        }
        return ctx.badRequest('A referrer link with this data already exists');
      }

      ctx.throw(500, 'Failed to update referrer link');
    }
  },

  async trackClick(ctx) {
    try {
      const { url, shortLink } = ctx.request.body;
      const { user } = ctx.state;

      if (!url && !shortLink) {
        return ctx.badRequest('Either URL or short link is required');
      }

      console.log('Tracking click for URL:', url, 'shortLink:', shortLink);

      // Pass user and shortLink to the enhanced service method
      const result = await strapi
        .service('api::referrer-link.referrer-link')
        .trackClickByUrl(url, user, shortLink);

      console.log(
        `Click tracked for link: ${result.linkName} (matched by: ${result.matchedBy}). New visitors count: ${result.visitors}`
      );

      return {
        success: true,
        message: 'Click tracked successfully',
        data: result,
      };
    } catch (err) {
      console.error('Error tracking click:', err);

      if (err.message?.includes('No matching referrer link found')) {
        return ctx.notFound(err.message);
      }

      ctx.throw(500, 'Failed to track click');
    }
  },

  async findByShortLink(ctx) {
    try {
      const { shortLink } = ctx.params;

      if (!shortLink) {
        return ctx.badRequest('Short link is required');
      }

      console.log('Searching for referrer link with short_link:', shortLink);

      // Find the referrer link by short_link
      const referrerLinks = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          filters: { short_link: shortLink },
          populate: ['referrer', 'user'],
          limit: 1,
        }
      );

      if (!referrerLinks || referrerLinks.length === 0) {
        return ctx.notFound(`No referrer link found with short link: ${shortLink}`);
      }

      const link = referrerLinks[0];

      return {
        success: true,
        data: this.transformResponse(link),
      };
    } catch (err) {
      console.error('Error finding referrer link by short link:', err);
      ctx.throw(500, 'Failed to find referrer link');
    }
  },

  async trackClickByShortLink(ctx) {
    try {
      const { shortLink } = ctx.request.body;
      const { user } = ctx.state;

      if (!shortLink) {
        return ctx.badRequest('Short link is required');
      }

      console.log('Tracking click for short link:', shortLink);

      // Pass user to the service method
      const result = await strapi
        .service('api::referrer-link.referrer-link')
        .trackClickByShortLink(shortLink, user);

      console.log(
        `Click tracked for short link: ${result.shortLink}. New visitors count: ${result.visitors}`
      );

      return {
        success: true,
        message: 'Click tracked successfully via short link',
        data: result,
      };
    } catch (err) {
      console.error('Error tracking click by short link:', err);

      if (err.message?.includes('No referrer link found')) {
        return ctx.notFound(err.message);
      }

      ctx.throw(500, 'Failed to track click by short link');
    }
  },

  async generateShortLinksForExisting(ctx) {
    try {
      const { user } = ctx.state;

      if (!user) {
        return ctx.unauthorized('You must be logged in to generate short links');
      }

      console.log('Generating short links for existing referrer links for user:', user.id);

      // Find all referrer links for this user that don't have short_link
      const referrerLinks = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          filters: {
            user: user.id,
            short_link: { $null: true },
          },
        }
      );

      if (!referrerLinks || referrerLinks.length === 0) {
        return {
          success: true,
          message: 'All referrer links already have short links',
          updated: 0,
        };
      }

      let updatedCount = 0;

      // Generate short links for each referrer link
      for (const link of referrerLinks) {
        const shortLink = await generateShortLink();

        await strapi.entityService.update('api::referrer-link.referrer-link', link.id, {
          data: {
            short_link: shortLink,
          },
        });

        updatedCount++;
      }

      console.log(`Generated short links for ${updatedCount} referrer links`);

      return {
        success: true,
        message: `Generated short links for ${updatedCount} referrer links`,
        updated: updatedCount,
      };
    } catch (err) {
      console.error('Error generating short links for existing referrer links:', err);
      ctx.throw(500, 'Failed to generate short links');
    }
  },

  /**
   * Bulk update totalClicks, totalLeads, and totalConversions for all referrers
   * This is an administrative endpoint that recalculates all referrer statistics
   */
  async updateAllReferrerTotals(ctx) {
    try {
      // Get all referrers in the system
      const allReferrers = await strapi.entityService.findMany('api::referrer.referrer', {
        fields: ['id'],
        pagination: { limit: -1 }, // Get all referrers
      });

      if (!allReferrers || allReferrers.length === 0) {
        return {
          success: true,
          message: 'No referrers found in the system',
          processed: 0,
          errors: [],
          timestamp: new Date().toISOString(),
        };
      }

      strapi.log.info(`Found ${allReferrers.length} referrers to update`);

      const results = {
        processed: 0,
        errors: [],
        details: {
          totalClicksUpdated: 0,
          totalLeadsUpdated: 0,
          totalConversionsUpdated: 0,
        },
      };

      const referrerLinkService = strapi.service('api::referrer-link.referrer-link');

      // Process each referrer
      for (const referrer of allReferrers) {
        try {
          strapi.log.info(`Processing referrer ${referrer.id}`);

          // Update totalClicks
          try {
            await referrerLinkService.updateReferrerTotalClicks(referrer.id);
            results.details.totalClicksUpdated++;
          } catch (error) {
            strapi.log.error(`Error updating totalClicks for referrer ${referrer.id}:`, error);
            results.errors.push({
              referrerId: referrer.id,
              operation: 'updateTotalClicks',
              error: error.message,
            });
          }

          // Update totalLeads
          try {
            await referrerLinkService.updateReferrerTotalLeads(referrer.id);
            results.details.totalLeadsUpdated++;
          } catch (error) {
            strapi.log.error(`Error updating totalLeads for referrer ${referrer.id}:`, error);
            results.errors.push({
              referrerId: referrer.id,
              operation: 'updateTotalLeads',
              error: error.message,
            });
          }

          // Update totalConversions
          try {
            await referrerLinkService.updateReferrerTotalConversions(referrer.id);
            results.details.totalConversionsUpdated++;
          } catch (error) {
            strapi.log.error(`Error updating totalConversions for referrer ${referrer.id}:`, error);
            results.errors.push({
              referrerId: referrer.id,
              operation: 'updateTotalConversions',
              error: error.message,
            });
          }

          results.processed++;
          strapi.log.info(`Successfully processed referrer ${referrer.id}`);
        } catch (error) {
          strapi.log.error(`Error processing referrer ${referrer.id}:`, error);
          results.errors.push({
            referrerId: referrer.id,
            operation: 'general',
            error: error.message,
          });
        }
      }

      const successMessage = `Bulk update completed. Processed ${results.processed}/${allReferrers.length} referrers.`;
      strapi.log.info(successMessage, results);

      return {
        success: true,
        message: successMessage,
        processed: results.processed,
        total: allReferrers.length,
        errors: results.errors,
        details: results.details,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      strapi.log.error('Error in bulk update of referrer totals:', error);
      ctx.throw(500, `Bulk update failed: ${error.message}`);
    }
  },

  async getAvailablePages(ctx) {
    try {
      const { user } = ctx.state;

      if (!user) {
        return ctx.unauthorized('You must be logged in to view available pages');
      }

      // Get pages that belong to the user and are NOT linked to any referrer-link
      const pages = await strapi.documents('api::page.page').findMany({
        filters: {
          author: user.id,
          status: 'published', // Only published pages can be linked
          referrer_link: {
            // @ts-expect-error - $null operator is valid in Strapi but not in type definitions
            $null: true // Pages that don't have a referrer_link relationship
          }
        },
        fields: ['id', 'title', 'slug'],
        sort: { createdAt: 'desc' },
      });

      return ctx.send({
        data: pages,
        meta: {
          total: pages.length
        }
      });
    } catch (error) {
      console.error('Error fetching available pages for referrer link:', error);
      return ctx.internalServerError('Error fetching available pages');
    }
  },


}));
