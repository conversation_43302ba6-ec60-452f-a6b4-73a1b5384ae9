/**
 * Custom routes for top-ad
 */

export default {
  routes: [
    {
      method: 'GET',
      path: '/top-ads/search',
      handler: 'top-ad.searchTrendingAds',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/top-ads/transcript/:adId',
      handler: 'top-ad.getAdTranscript',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
