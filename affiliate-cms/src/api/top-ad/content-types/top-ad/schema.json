{"kind": "collectionType", "collectionName": "top_ads", "info": {"singularName": "top-ad", "pluralName": "top-ads", "displayName": "Top Ad", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"platform": {"type": "enumeration", "enum": ["tiktok", "youtube"]}, "ad_id": {"type": "string", "unique": true, "column": {"unique": true}, "required": true}, "ad_title": {"type": "text"}, "brand_name": {"type": "string"}, "ctr": {"type": "decimal"}, "cost": {"type": "integer"}, "industry_key": {"type": "string"}, "country_code": {"type": "json"}, "landing_page": {"type": "text"}, "likes": {"type": "integer"}, "comments": {"type": "integer"}, "shares": {"type": "integer"}, "published_from": {"type": "datetime"}, "objective_key": {"type": "string"}, "objectives": {"type": "json"}, "source": {"type": "string"}, "video_info": {"type": "json"}, "is_search": {"type": "boolean", "default": false}, "favorite": {"type": "boolean", "default": false}, "keyword": {"type": "string"}, "last_fetched": {"type": "datetime"}, "is_displayed": {"type": "boolean", "default": true}, "views": {"type": "integer", "default": 0}, "affiliate": {"type": "relation", "relation": "oneToOne", "target": "api::affiliate.affiliate"}}}