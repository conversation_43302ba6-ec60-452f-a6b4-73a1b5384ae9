export interface IUser {
  id?: number;
  users_permissions_user?: number;
  request_count?: number;
  request_limit?: number;
  last_request_date?: Date;
  statistics?: Record<string, number>;
  subscription_tier?: number | ISubscriptionTier;

  // Strapi fields
  createdAt?: Date;
  updatedAt?: Date;
  publishedAt?: Date;
}

export interface ISubscriptionTier {
  id?: number;
  name: string;
  display_name: string;
  price: number;
  request_limit: number;
  duration_days: number;
  features?: Record<string, any>;
  is_popular?: boolean;
  description?: string;

  // Relations
  transactions?: ITransaction[];

  // Strapi fields
  createdAt?: Date;
  updatedAt?: Date;
  publishedAt?: Date;
}

export interface ITransaction {
  id: number;
  user: any;
  subscription_tier: any;
  amount: number;
  currency: string;
  payment_status: 'pending' | 'completed' | 'failed' | 'refunded';
  payment_method: 'stripe' | 'free';
  transaction_date: Date;
  payment_details?: any;
  stripe_invoice_id?: string;
}

export interface StripeSessionResponse {
  id: string;
  url: string;
  status: string;
  client_secret?: string;
}

export interface TrackingResult {
  allowed: boolean;
  remaining: number;
  limit: number;
  count: number;
  tier: string;
  pathStats?: number;
}
