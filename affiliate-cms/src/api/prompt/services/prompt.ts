/**
 * prompt service
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::prompt.prompt', ({ strapi }) => ({
  async getDefaultPrompt() {
    try {
      const globalSettings = await strapi.entityService.findMany('api::global.global');

      return globalSettings?.defaultPrompt
        ? {
            content: globalSettings.defaultPrompt,
          }
        : null;
    } catch (error) {
      console.error('Error fetching default prompt:', error);
      return null;
    }
  },

  async getSummaryPrompt() {
    try {
      const globalSettings = await strapi.entityService.findMany('api::global.global');
      return globalSettings?.summaryPrompt
        ? {
            content: globalSettings.summaryPrompt,
          }
        : null;
    } catch (error) {
      console.error('Error fetching summary prompt:', error);
      return null;
    }
  },

  async getPromptById(promptId: string) {
    try {
      // For findOne, we need to provide the ID directly and can't use filters parameter
      const prompt = await strapi.entityService.findOne('api::prompt.prompt', promptId);

      // Then check if it's published here
      if (prompt && prompt.publishedAt) {
        return prompt;
      }
      return null;
    } catch (error) {
      console.error(`Error fetching prompt with ID ${promptId}:`, error);
      return null;
    }
  },
}));
