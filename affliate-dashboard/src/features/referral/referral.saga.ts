import { call, put, takeEvery } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import axios from "axios";
import { actions } from "./referral.slice";

function* fetchReferralsSaga(
  action: PayloadAction<{
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
    isAdmin?: boolean;
    sort?: string;
  }>
): Generator<any, void, any> {
  try {
    const {
      page = 1,
      pageSize = 25,
      search = "",
      status = "",
      isAdmin = false,
      sort,
    } = action.payload;

    // Get appropriate token based on admin status
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem(isAdmin ? "admin_token" : "auth_token")
        : null;

    if (!token) {
      throw new Error("Authentication required");
    }

    // Use appropriate API endpoint
    const apiUrl = isAdmin ? "/api/admin/referrals" : "/api/referrals";

    const response = yield call(axios.get, apiUrl, {
      params: {
        page,
        pageSize,
        search,
        status,
        ...(sort && { sort }),
      },
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    yield put(actions.fetchReferralsSuccess(response.data));
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      "Failed to fetch referrals";
    yield put(actions.fetchReferralsFailure(errorMessage));
  }
}

export default function* referralSaga() {
  yield takeEvery(actions.fetchReferralsRequest.type, fetchReferralsSaga);
}
