import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "@/store";
import { createSelector } from "reselect";

export interface ReferrerLink {
  id: string;
  documentId?: string; // Optional for compatibility with Strapi
  name: string;
  url: string;
  short_link?: string; // Short link for easy sharing
  visitors: number;
  leads: number;
  conversions: number;
  // Additional view tracking fields (visitors is the primary field)
  direct_page_views?: number;
  referrer_link_views?: number;
  short_link_views?: number;
  referrer_sources?: Record<string, number>; // JSON object storing referrer information and counts
  // Page relationship
  page?: {
    id: number;
    documentId: string;
    title: string;
    slug: string;
  };
}

interface ReferrerLinksState {
  links: ReferrerLink[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
  } | null;
}

const initialState: ReferrerLinksState = {
  links: [],
  loading: false,
  error: null,
  pagination: null,
};

const referrerLinksSlice = createSlice({
  name: "referrerLinks",
  initialState,
  reducers: {
    // Fetch referrer links - Fix the action definition
    fetchLinks: (
      state,
      action: PayloadAction<{ page?: number; pageSize?: number }>
    ) => {
      state.loading = true;
      state.error = null;
    },
    fetchLinksSuccess: (state, action: PayloadAction<ReferrerLink[]>) => {
      // When loading a new page, append to existing links instead of replacing
      if (state.pagination && state.pagination.page > 1) {
        // Filter out duplicates by id
        const existingIds = new Set(state.links.map((link) => link.id));
        const newLinks = action.payload.filter(
          (link) => !existingIds.has(link.id)
        );
        state.links = [...state.links, ...newLinks];
      } else {
        state.links = action.payload;
      }
      state.loading = false;
    },
    fetchLinksFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Create a new link
    createLink: (
      state,
      action: PayloadAction<{ name: string; url: string; shortLink?: string; selectedPage?: string }>
    ) => {
      state.loading = true;
      state.error = null;
    },
    createLinkSuccess: (
      state,
      action: PayloadAction<{ data: ReferrerLink }>
    ) => {
      // Don't push to existing array - will trigger fetchLinks instead
      state.loading = false;
    },

    // Update a link
    updateLink: (
      state,
      action: PayloadAction<{
        id: string;
        documentId: string;
        name: string;
        url: string;
        shortLink?: string;
        selectedPage?: string;
      }>
    ) => {
      state.loading = true;
      state.error = null;
    },
    updateLinkSuccess: (state, action: PayloadAction<ReferrerLink | null>) => {
      // Just set loading to false - the fetchLinks call will update the actual data
      state.loading = false;
    },

    // Delete a link
    deleteLink: (
      state,
      action: PayloadAction<{ id: string; documentId: string }>
    ) => {
      state.loading = true;
      state.error = null;
    },
    deleteLinkSuccess: (state, action: PayloadAction<string>) => {
      state.links = state.links.filter((link) => link.id !== action.payload);
      state.loading = false;
    },

    // Set pagination
    setPagination: (
      state,
      action: PayloadAction<{
        page: number;
        pageSize: number;
        pageCount: number;
        total: number;
      }>
    ) => {
      state.pagination = action.payload;
    },

    // Common error handler
    setError: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Reset error state
    clearError: (state) => {
      state.error = null;
    },

    // Fetch short link by short code
    fetchShortLink: (state, action: PayloadAction<{ shortCode: string }>) => {
      state.loading = true;
      state.error = null;
    },
    fetchShortLinkSuccess: (state, action: PayloadAction<any>) => {
      state.loading = false;
    },
    fetchShortLinkFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
  },
});

export const { actions, reducer } = referrerLinksSlice;

// Selectors
export const selectReferrerLinksState = (state: RootState) =>
  state.referrerLinks;
export const selectReferrerLinks = createSelector(
  [selectReferrerLinksState],
  (state) => state.links
);
export const selectReferrerLinksLoading = createSelector(
  [selectReferrerLinksState],
  (state) => state.loading
);
export const selectReferrerLinksError = createSelector(
  [selectReferrerLinksState],
  (state) => state.error
);
export const selectReferrerLinksPagination = createSelector(
  [selectReferrerLinksState],
  (state) => state.pagination
);

// Selector for short link operations
export const selectReferrerLinksShortLinkError = createSelector(
  [selectReferrerLinksState],
  (state) => state.error
);
