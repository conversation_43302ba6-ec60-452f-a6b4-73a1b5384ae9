import { IPagination, IVideo, ISort } from "@/interfaces";
import { createSelector } from "reselect";
import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { VideoFilterState } from "@/containers/TopVideos/FilterPanel";

const selectTopVideosState = (state: RootState) => state.topVideos;

interface TopVideosState {
  list: IVideo[] | null;
  loading: boolean;
  pagination: IPagination;
  error: string | null;
  sort: { field: string; order: "desc" | "asc" };
  timePeriod: string;
  filters: VideoFilterState;
  searchTerm: string;
}

const initialState: TopVideosState = {
  list: null,
  loading: false,
  pagination: {
    page: 1,
    pageSize: 10,
    pageCount: 0,
    total: 0,
  },
  error: null,
  sort: { field: "views", order: "desc" },
  timePeriod: "30days",
  filters: {
    platforms: [],
    categories: [],
    minViews: "",
  },
  searchTerm: "",
};

const topVideosSlice = createSlice({
  name: "top-videos",
  initialState,
  reducers: {
    fetchTopVideos(
      state,
      action: PayloadAction<{
        platforms: string[];
        pagination: IPagination;
        sort: ISort[];
        dateFilter?: any;
        searchTerm?: string;
      }>
    ) {
      state.loading = true;
    },
    setTopVideos(state, action: PayloadAction<IVideo[] | null>) {
      state.loading = false;
      if (state.list && state.list.length && action.payload) {
        const ids = new Set(state.list.map((item) => item.documentId));
        state.list = [
          ...state.list,
          ...action.payload.filter((item) => !ids.has(item.documentId)),
        ];
      } else {
        state.list = action.payload;
      }
    },
    setPagination(state, action: PayloadAction<IPagination>) {
      state.pagination = action.payload;
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
      state.loading = false;
    },
    setSortOption(
      state,
      action: PayloadAction<{ field: string; order: "desc" | "asc" }>
    ) {
      state.sort = action.payload;
    },
    setTimePeriod(state, action: PayloadAction<string>) {
      state.timePeriod = action.payload;
    },
    setFilters(state, action: PayloadAction<VideoFilterState>) {
      state.filters = action.payload;
    },
    setSearchTerm(state, action: PayloadAction<string>) {
      state.searchTerm = action.payload;
    },
  },
});

export const reducer = topVideosSlice.reducer;
export const actions = topVideosSlice.actions;

export const selectTopVideosList = createSelector(
  [selectTopVideosState],
  (topVideosState) => topVideosState.list
);

export const selectTopVideosLoading = createSelector(
  [selectTopVideosState],
  (topVideosState) => topVideosState.loading
);

export const selectTopVideosPagination = createSelector(
  [selectTopVideosState],
  (topVideosState) => topVideosState.pagination
);

export const selectTopVideosError = createSelector(
  [selectTopVideosState],
  (topVideosState) => topVideosState.error
);
