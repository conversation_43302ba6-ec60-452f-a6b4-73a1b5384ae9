import React, { useState, useRef } from 'react';
import YooptaRichTextEditor from '@/components/YooptaRichTextEditor';
import type { YooptaContentValue } from '@/types/yoopta-editor';


const TestEditor: React.FC = () => {
  const [content, setContent] = useState<YooptaContentValue | undefined>(undefined);
  const [testResults, setTestResults] = useState<{[key: string]: boolean}>({});
  const [lastChangeTime, setLastChangeTime] = useState<number>(0);
  const editorContainerRef = useRef<HTMLDivElement>(null);

  const handleContentChange = (newContent: YooptaContentValue) => {
    console.log('📝 [Test Editor] Content changed:', newContent);
    setContent(newContent);
    setLastChangeTime(Date.now());

    // Test: Content input is working
    setTestResults(prev => ({
      ...prev,
      contentInput: Object.keys(newContent).length > 0
    }));
  };

  const handleSave = (content: YooptaContentValue) => {
    console.log('📝 [Test Editor] Auto-save triggered:', content);

    // Test: Auto-save is working
    setTestResults(prev => ({
      ...prev,
      autoSave: true
    }));
  };

  // Test keyboard events
  const handleKeyDown = (e: React.KeyboardEvent) => {
    console.log('📝 [Test Editor] Key pressed:', e.key);

    if (e.key === 'Enter') {
      setTestResults(prev => ({ ...prev, enterKey: true }));
    }
    if (e.key === '/') {
      setTestResults(prev => ({ ...prev, slashCommand: true }));
    }
    if (e.key === 'Backspace') {
      setTestResults(prev => ({ ...prev, backspace: true }));
    }
  };

  // Test focus functionality
  const testFocus = () => {
    console.log('📝 [Test Editor] Testing focus functionality');
    if (editorContainerRef.current) {
      const focusableElements = editorContainerRef.current.querySelectorAll('[contenteditable="true"], [data-yoopta-editor], .yoopta-editor');
      console.log('📝 [Test Editor] Found focusable elements:', focusableElements.length);

      if (focusableElements.length > 0) {
        (focusableElements[0] as HTMLElement).focus();
        setTestResults(prev => ({ ...prev, programmaticFocus: true }));
      } else {
        // Try focusing the container
        editorContainerRef.current.focus();
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border p-8">
          <h1 className="text-2xl font-bold mb-6">Yoopta Editor Test</h1>
          <div className="prose max-w-none">
            <p className="text-gray-600 mb-6">
              This is a test page for the Yoopta Rich Text Editor. Try the following:
            </p>
            <ul className="text-gray-600 mb-8">
              <li>Type "/" to trigger the action menu (slash commands)</li>
              <li>Try typing and editing content</li>
              <li>Test different formatting options</li>
              <li>Check the browser console for debug logs</li>
            </ul>
          </div>
          
          <div className="border-t pt-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Editor:</h2>
              <button
                onClick={testFocus}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
              >
                Test Focus
              </button>
            </div>
            <div
              ref={editorContainerRef}
              className="min-h-[400px] border rounded-lg p-4"
              onKeyDown={handleKeyDown}
            >
              <YooptaRichTextEditor
                content={content}
                onChange={handleContentChange}
                onSave={handleSave}
                autoSave={true}
                autoSaveInterval={3000}
                loading={false}
                placeholder="Type '/' for commands..."
                className="test-editor"
              />
            </div>
          </div>

          {/* Test Results Panel */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold mb-2">Functionality Test Results:</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className={`p-2 rounded ${testResults.contentInput ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                ✓ Content Input: {testResults.contentInput ? 'Working' : 'Not tested'}
              </div>
              <div className={`p-2 rounded ${testResults.autoSave ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                ✓ Auto-save: {testResults.autoSave ? 'Working' : 'Not tested'}
              </div>
              <div className={`p-2 rounded ${testResults.enterKey ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                ✓ Enter Key: {testResults.enterKey ? 'Working' : 'Not tested'}
              </div>
              <div className={`p-2 rounded ${testResults.slashCommand ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                ✓ Slash Commands: {testResults.slashCommand ? 'Working' : 'Not tested'}
              </div>
              <div className={`p-2 rounded ${testResults.backspace ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                ✓ Backspace: {testResults.backspace ? 'Working' : 'Not tested'}
              </div>
              <div className={`p-2 rounded ${testResults.programmaticFocus ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                ✓ Programmatic Focus: {testResults.programmaticFocus ? 'Working' : 'Not tested'}
              </div>
              <div className="p-2 rounded bg-gray-100 text-gray-600">
                Last Change: {lastChangeTime ? new Date(lastChangeTime).toLocaleTimeString() : 'None'}
              </div>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-gray-100 rounded-lg">
            <h3 className="font-semibold mb-2">Current Content (JSON):</h3>
            <pre className="text-xs overflow-auto max-h-40">
              {JSON.stringify(content, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestEditor;
