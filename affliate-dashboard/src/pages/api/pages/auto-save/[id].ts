import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;
  const { id } = req.query;

  if (method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ 
      statusCode: 400, 
      message: "Page ID is required" 
    });
  }

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    const { content } = req.body;

    if (content === undefined) {
      return res.status(400).json({ 
        statusCode: 400, 
        message: "Content is required for auto-save" 
      });
    }

    // Call Strapi API to auto-save page
    const response = await StrapiClient.client.post(`/api/pages/auto-save/${id}`, {
      content,
    }, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return res.status(200).json(response.data);
  } catch (error: any) {
    console.error("Error auto-saving page:", error);
    return sendApiError(res, error, "Failed to auto-save page");
  }
}
