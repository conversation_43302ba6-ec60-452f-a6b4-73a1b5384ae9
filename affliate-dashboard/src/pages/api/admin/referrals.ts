import { StrapiAdminClient } from "@/utils/request";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Get authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: "Authorization header required" });
    }

    const token = authHeader.replace("Bearer ", "");

    // Extract query parameters
    const {
      page = 1,
      pageSize = 10,
      search = "",
      status = "",
      sort = "id:ASC",
    } = req.query;

    // Use StrapiAdminClient.getReferrals to fetch referrals
    const data = (await StrapiAdminClient.getReferrals(
      {
        page: Number(page),
        pageSize: Number(pageSize),
        search: search as string,
        sort: sort as string,
      },
      token
    )) as any;

    // Transform the data to match our expected format
    const transformedData = {
      data:
        data.results?.map((referral: any) => ({
          id: referral.id,
          documentId: referral.documentId,
          referral_status: referral.referral_status,
          total_paid: referral.total_paid || 0,
          total_commission: referral.total_commission || 0,
          createdAt: referral.createdAt,
          updatedAt: referral.updatedAt,
          publishedAt: referral.publishedAt,
          user: referral.user
            ? {
                id: referral.user.id,
                documentId: referral.user.documentId,
                username: referral.user.username,
                email: referral.user.email,
                first_name: referral.user.first_name,
                last_name: referral.user.last_name,
              }
            : null,
          referrer: referral.referrer
            ? {
                id: referral.referrer.id,
                documentId: referral.referrer.documentId,
                referral_code: referral.referrer.referral_code,
                user: referral.referrer.user
                  ? {
                      first_name: referral.referrer.user.first_name,
                      last_name: referral.referrer.user.last_name,
                      email: referral.referrer.user.email,
                      username: referral.referrer.user.username,
                    }
                  : null,
              }
            : null,
        })) || [],
      meta: {
        pagination: data.pagination || {
          page: parseInt(page.toString()),
          pageSize: parseInt(pageSize.toString()),
          pageCount: 0,
          total: 0,
        },
      },
    };

    res.status(200).json(transformedData);
  } catch (error: any) {
    console.error("API Error:", error);
    res.status(500).json({
      error: "Failed to fetch referrals",
      details: error.message,
    });
  }
}
