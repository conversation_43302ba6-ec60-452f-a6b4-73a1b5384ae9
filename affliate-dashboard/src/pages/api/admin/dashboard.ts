import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient } from "@/utils/request";

// Helper function to format time ago
function formatTimeAgo(dateString: string): string {
  const now = new Date();
  const date = new Date(dateString);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return `${diffInSeconds} sec ago`;
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} min ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? "s" : ""} ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? "s" : ""} ago`;
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Get admin token from headers
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({ error: "Admin authentication required" });
    }

    const token = authHeader.replace("Bearer ", "");

    // Get dashboard stats
    const data: any = await StrapiAdminClient.getDashboardStats(token);

    // Transform the data to match UI expectations
    const transformedData = {
      success: true,
      data: {
        totalReferrers: data.data?.totalReferrers || 0,
        totalReferrals: data.data?.totalReferrals || 0,
        totalRevenue: data.data?.totalRevenue || 0,
        totalClicks: data.data?.totalClicks || 0,
        totalLeads: data.data?.totalLeads || 0,
        currentMonthRevenue: data.data?.currentMonthRevenue || 0,
        previousMonthRevenueAmount: data.data?.previousMonthRevenueAmount || 0,
        growthRate: data.data?.growthRate || 0,
        recentActivity: (data.data?.recentActivity || []).map(
          (activity: any) => ({
            id: activity.id,
            documentId: activity.documentId,
            amount_paid: activity.amount_paid,
            referral_status: activity.referral_status,
            description: activity.description,
            createdAt: activity.createdAt,
            updatedAt: activity.updatedAt,
            publishedAt: activity.publishedAt,
            locale: activity.locale,
            referral: activity.referral
          })
        ),
        topReferrers: (data.data?.topReferrers || []).map(
          (referrer: any) => ({
            id: referrer.id,
            documentId: referrer.documentId,
            referral_code: referrer.referral_code,
            referrer_status: referrer.referrer_status,
            createdAt: referrer.createdAt,
            updatedAt: referrer.updatedAt,
            publishedAt: referrer.publishedAt,
            locale: referrer.locale,
            balance: referrer.balance,
            total_earnings: referrer.total_earnings,
            user: referrer.user,
            totalClicks: referrer.totalClicks,
            totalLeads: referrer.totalLeads,
            totalConversions: referrer.totalConversions,
            totalCustomers: referrer.totalCustomers,
            totalEarnings: referrer.totalEarnings,
            totalRevenue: referrer.totalRevenue,
            commissionStats: referrer.commissionStats,
          })
        ),
        topReferrals: (data.data?.topReferrals || []).map(
          (referral: any) => ({
            id: referral.id,
            documentId: referral.documentId,
            referral_status: referral.referral_status,
            total_paid: referral.total_paid,
            createdAt: referral.createdAt,
            updatedAt: referral.updatedAt,
            publishedAt: referral.publishedAt,
            locale: referral.locale,
            user: referral.user,
          })
        ),
      },
    };

    console.log('transformedData:', transformedData);
    res.status(200).json(transformedData);
  } catch (error: any) {
    console.error("Admin dashboard API error:", error);

    // For now, return mock data if the endpoint doesn't exist
    if (error.statusCode === 404) {
      const mockData = {
        success: true,
        data: {
          totalReferrers: 18,
          totalReferrals: 5,
          totalRevenue: 38,
          currentMonthRevenue: 38,
          previousMonthRevenueAmount: 0,
          growthRate: 100,
          recentActivity: [
            {
              id: 1,
              type: "click",
              message: "User clicked referral link",
              time: "2 min ago",
              amount_paid: null,
              referral_status: "click",
            },
            {
              id: 2,
              type: "conversion",
              message: "Subscribed to Pro with amount 38",
              time: "5 min ago",
              amount_paid: 38,
              referral_status: "conversion",
            },
            {
              id: 3,
              type: "lead",
              message: "Registered new user",
              time: "10 min ago",
              amount_paid: null,
              referral_status: "lead",
            },
          ],
        },
      };
      return res.status(200).json(mockData);
    }

    // Handle other errors
    if (error.statusCode) {
      return res.status(error.statusCode).json({
        error: error.message || "Failed to fetch dashboard data",
      });
    }

    res.status(500).json({ error: "Internal server error" });
  }
}
