import { NextApiRequest, NextApiResponse } from "next";
import { AppError } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";

interface AffiliateUrlResponse {
  url: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<AffiliateUrlResponse | AppError>
) {
  if (req.method === "GET") {
    try {
      const { id } = req.query;
      
      if (!id || typeof id !== "string") {
        return res.status(400).json({ statusCode: 400, message: "Invalid ID" });
      }

      const response: any = await StrapiClient.getAffiliateUrl(id);
      res.status(200).json(response);
    } catch (error: any) {
      console.error("Error fetching affiliate URL:", error);
      sendApiError(res, error, "Error fetching affiliate URL");
    }
  } else {
    res.setHeader("Allow", ["GET"]);
    res.status(405).json({ statusCode: 405, message: `Method ${req.method} Not Allowed` });
  }
}
