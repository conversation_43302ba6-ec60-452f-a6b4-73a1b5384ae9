import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== "POST") {
    return res
      .status(405)
      .json({ statusCode: 405, message: "Method not allowed" });
  }

  try {
    const { action, token, ...data } = req.body;

    console.log("Received Discourse action:", action);

    switch (action) {
      case "get-login-url": {
        // Get the direct login URL for Discourse (forward flow)
        const response = await StrapiClient.getDiscourseLoginUrl();
        return res.status(200).json(response);
      }

      case "process-sso-params": {
        if (!token) {
          return res.status(401).json({
            statusCode: 401,
            message: "Authentication token is required",
          });
        }

        // Extract SSO parameters (required for reverse flow)
        const { sso, sig } = data;

        if (!sso || !sig) {
          return res.status(400).json({
            statusCode: 400,
            message: "SSO parameters (sso and sig) are required",
          });
        }

        // Process SSO parameters from Discourse
        const response = await StrapiClient.processDiscourseSSOParams(token, sso, sig);
        return res.status(200).json(response);
      }

      case "get-config": {
        // Get Discourse configuration (public endpoint)
        const response = await StrapiClient.getDiscourseConfig();
        return res.status(200).json(response);
      }

      default:
        return res
          .status(400)
          .json({ statusCode: 400, message: "Invalid action" });
    }
  } catch (error: any) {
    console.error("Discourse API route error:", error);
    sendApiError(res, error, "Discourse service error");
  }
}
