import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import qs from "qs"; // Import qs for query string serialization
import { sendApiError } from "@/utils/api-error-handler";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // Serialize the query object into a query string
    const queryString = qs.stringify(req.query, { encodeValuesOnly: true });

    // Pass the serialized query string to StrapiClient
    const response = await StrapiClient.getTrafficWeb(queryString);

    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching traffic web data:", error);
    return sendApiError(res, error, "Error fetching traffic web data");
  }
}