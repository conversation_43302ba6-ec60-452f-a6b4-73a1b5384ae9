import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { actions } from '@/features/page/page.slice';
import {
  selectCreatePageLoading,
  selectCreatePageError,
  selectPageData,
} from '@/features/page/page.slice';

const TestPageCreation: React.FC = () => {
  const dispatch = useDispatch();
  const [title, setTitle] = useState('Test Page');
  
  const createLoading = useSelector(selectCreatePageLoading);
  const createError = useSelector(selectCreatePageError);
  const pages = useSelector(selectPageData);

  const handleCreatePage = () => {
    const newPageData = {
      title,
      content: {
        'block-1': {
          id: 'block-1',
          type: 'paragraph',
          value: [{ text: 'This is a test page created for testing the page creation functionality.' }],
          meta: { order: 0, depth: 0 }
        },
        'block-2': {
          id: 'block-2',
          type: 'heading-one',
          value: [{ text: 'Welcome to Your New Page' }],
          meta: { order: 1, depth: 0 }
        }
      },
      excerpt: 'This is a test page for verifying the page creation system.',
      status: 'draft' as const,
    };

    dispatch(actions.createPageRequest(newPageData));
  };

  const handleFetchPages = () => {
    dispatch(actions.fetchPagesRequest({ page: 1, pageSize: 10 }));
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Page Creation Test</h1>
      
      <div className="space-y-4 mb-8">
        <div>
          <label className="block text-sm font-medium mb-2">Page Title</label>
          <Input
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter page title"
          />
        </div>
        
        <div className="flex gap-4">
          <Button
            onClick={handleCreatePage}
            disabled={createLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {createLoading ? 'Creating...' : 'Create Test Page'}
          </Button>
          
          <Button
            onClick={handleFetchPages}
            variant="outline"
          >
            Fetch Pages
          </Button>
        </div>
        
        {createError && (
          <div className="text-red-600 text-sm">
            Error: {createError}
          </div>
        )}
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-4">Your Pages ({pages.length})</h2>
        <div className="space-y-2">
          {pages.map((page) => (
            <div
              key={page.documentId}
              className="p-4 border rounded-lg bg-gray-50"
            >
              <h3 className="font-medium">{page.title}</h3>
              <p className="text-sm text-gray-600">{page.excerpt}</p>
              <div className="flex items-center gap-2 mt-2">
                <span className={`px-2 py-1 text-xs rounded-full ${
                  page.status === 'published' 
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {page.status}
                </span>
                <span className="text-xs text-gray-500">
                  {new Date(page.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TestPageCreation;
