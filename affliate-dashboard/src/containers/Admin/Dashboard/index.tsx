"use client";
import { useSelector, useDispatch } from "react-redux";
import { useEffect } from "react";
import {
  selectAdminData,
  selectAdminDashboardStats,
  selectAdminDashboardStatsLoading,
} from "@/features/selectors";
import { actions as adminActions } from "@/features/admin/admin.slice";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// Enhanced StatCard component
interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
    label?: string;
  };
  subtitle?: string;
  loading?: boolean;
  color: "blue" | "green" | "purple" | "orange" | "red" | "indigo";
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  trend,
  subtitle,
  loading = false,
  color,
}) => {
  const colorClasses = {
    blue: "bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400",
    green: "bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400",
    purple: "bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400",
    orange: "bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400",
    red: "bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400",
    indigo: "bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400",
  };

  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`p-3 rounded-xl ${colorClasses[color]}`}>
              {icon}
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                {title}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {loading ? (
                  <span className="animate-pulse">...</span>
                ) : (
                  value
                )}
              </p>
              {subtitle && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          {trend && (
            <div className="text-right">
              <div className={`flex items-center space-x-1 ${
                trend.isPositive ? "text-green-600" : "text-red-600"
              }`}>
                <span className="text-sm font-medium">
                  {trend.isPositive ? "↗" : "↘"} {Math.abs(trend.value)}%
                </span>
              </div>
              {trend.label && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {trend.label}
                </p>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default function AdminDashboard() {
  const dispatch = useDispatch();
  const adminData = useSelector(selectAdminData);
  const dashboardStats = useSelector(selectAdminDashboardStats);
  const isLoading = useSelector(selectAdminDashboardStatsLoading);

  useEffect(() => {
    // Fetch dashboard stats when component mounts
    dispatch(adminActions.fetchDashboardStats());
  }, [dispatch]);

  // Navigation handlers for View All buttons
  const handleViewAllPartners = () => {
    window.location.hash = "partners";
  };

  const handleViewAllCustomers = () => {
    window.location.hash = "customers";
  };

  return (
    <div className="p-6 bg-gray-50 dark:bg-gray-900 min-h-full">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Good morning, {adminData?.firstname}! 👋
        </p>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Partners"
          value={dashboardStats?.totalReferrers || 0}
          color="blue"
          loading={isLoading}
          subtitle="Active affiliate partners"
          icon={
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          }
        />

        <StatCard
          title="Total Revenue"
          value={`$${dashboardStats?.totalRevenue || 0}`}
          color="green"
          loading={isLoading}
          subtitle="All-time earnings"
          icon={
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          }
        />

        <StatCard
          title="Total Referrals"
          value={dashboardStats?.totalReferrals || 0}
          color="purple"
          loading={isLoading}
          subtitle="Successful conversions"
          icon={
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          }
        />

        <StatCard
          title="This Month"
          value={`$${dashboardStats?.currentMonthRevenue || 0}`}
          color="orange"
          loading={isLoading}
          subtitle="Monthly performance"
          trend={{
            value: dashboardStats?.growthRate || 0,
            isPositive: (dashboardStats?.growthRate || 0) >= 0,
            label: "vs last month"
          }}
          icon={
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          }
        />
      </div>

      {/* Enhanced Quick Summary */}
      <div className="mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Performance Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* Total Clicks */}
              <div className="text-center space-y-3">
                <div className="flex items-center justify-center w-12 h-12 mx-auto bg-blue-100 dark:bg-blue-900/20 rounded-full">
                  <svg className="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                  </svg>
                </div>
                <div>
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                    {isLoading ? (
                      <span className="animate-pulse">...</span>
                    ) : (
                      (dashboardStats?.totalClicks || 0).toLocaleString()
                    )}
                  </div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mt-1">
                    Total Clicks
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                  </p>
                </div>
              </div>

              {/* Total Leads */}
              <div className="text-center space-y-3">
                <div className="flex items-center justify-center w-12 h-12 mx-auto bg-purple-100 dark:bg-purple-900/20 rounded-full">
                  <svg className="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <div>
                  <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                    {isLoading ? (
                      <span className="animate-pulse">...</span>
                    ) : (
                      (dashboardStats?.totalLeads || 0).toLocaleString()
                    )}
                  </div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mt-1">
                    Total Leads
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    Sign-ups from partner links
                  </p>
                </div>
              </div>

              {/* Conversion Rate */}
              <div className="text-center space-y-3">
                <div className="flex items-center justify-center w-12 h-12 mx-auto bg-green-100 dark:bg-green-900/20 rounded-full">
                  <svg className="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                    {isLoading ? (
                      <span className="animate-pulse">...</span>
                    ) : (
                      `${((dashboardStats?.totalReferrals || 0) / Math.max(dashboardStats?.totalClicks || 1, 1) * 100).toFixed(1)}%`
                    )}
                  </div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mt-1">
                    Conversion Rate
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    Clicks to conversions
                  </p>
                </div>
                <div className="w-full">
                  <Progress
                    value={((dashboardStats?.totalReferrals || 0) / Math.max(dashboardStats?.totalClicks || 1, 1) * 100)}
                    className="h-2"
                  />
                </div>
              </div>

              {/* Average Revenue per Partner */}
              <div className="text-center space-y-3">
                <div className="flex items-center justify-center w-12 h-12 mx-auto bg-indigo-100 dark:bg-indigo-900/20 rounded-full">
                  <svg className="h-6 w-6 text-indigo-600 dark:text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <div>
                  <div className="text-3xl font-bold text-indigo-600 dark:text-indigo-400">
                    {isLoading ? (
                      <span className="animate-pulse">...</span>
                    ) : (
                      `$${((dashboardStats?.totalRevenue || 0) / Math.max(dashboardStats?.totalReferrers || 1, 1)).toFixed(0)}`
                    )}
                  </div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mt-1">
                    Avg Revenue/Partner
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    Revenue per active partner
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Content Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Enhanced Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <svg className="h-5 w-5 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Recent Activity</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {isLoading ? (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  Loading activity...
                </div>
              ) : dashboardStats?.recentActivity?.length > 0 ? (
                dashboardStats.recentActivity.map((activity: any) => (
                  <div
                    key={activity.id}
                    className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 hover:shadow-sm"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                        <div
                          className={`w-3 h-3 rounded-full flex-shrink-0 ${
                            activity.referral_status === "conversion"
                              ? "bg-green-500"
                              : activity.referral_status === "click"
                              ? "bg-blue-500"
                              : activity.referral_status === "lead"
                              ? "bg-purple-500"
                              : "bg-gray-500"
                          }`}
                        ></div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 truncate">
                            {activity.description}
                          </p>
                          {activity.referral?.user && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                              {activity.referral.user.username ||
                                activity.referral.user.email}
                              {activity.referral.total_paid > 0 && (
                                <span className="text-green-600 ml-2 font-medium">
                                  (${activity.referral.total_paid})
                                </span>
                              )}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="text-right flex-shrink-0 ml-3">
                        <Badge
                          variant={
                            activity.referral_status === "conversion"
                              ? "default"
                              : activity.referral_status === "click"
                              ? "secondary"
                              : "outline"
                          }
                          className={`text-xs ${
                            activity.referral_status === "conversion"
                              ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                              : activity.referral_status === "click"
                              ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                              : activity.referral_status === "lead"
                              ? "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400"
                              : "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
                          }`}
                        >
                          {activity.referral_status?.charAt(0).toUpperCase() +
                            activity.referral_status?.slice(1) || "Unknown"}
                        </Badge>
                        <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                          {new Date(activity.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  <svg className="h-12 w-12 mx-auto mb-2 text-gray-300 dark:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  No recent activity
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Top Partners */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center space-x-2">
                <svg className="h-5 w-5 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span>Top Partners</span>
              </CardTitle>
              <button
                onClick={handleViewAllPartners}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium transition-colors"
              >
                View All →
              </button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {isLoading ? (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  Loading partners...
                </div>
              ) : dashboardStats?.topReferrers?.length > 0 ? (
                dashboardStats.topReferrers
                  .slice(0, 8)
                  .map((partner: any, index: number) => (
                    <div
                      key={partner.id}
                      className="flex items-center justify-between p-4 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer transition-all duration-200 border border-gray-100 dark:border-gray-700"
                    >
                      <div className="flex items-center space-x-3 min-w-0 flex-1">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0 shadow-sm">
                          {index + 1}
                        </div>
                        <div className="min-w-0 flex-1">
                          <p
                            className="font-semibold text-gray-900 dark:text-gray-100 truncate"
                            title={
                              partner.user?.username || partner.referral_code
                            }
                          >
                            {partner.user?.username || partner.referral_code}
                          </p>
                          <p
                            className="text-sm text-gray-500 dark:text-gray-400 truncate"
                            title={partner.user?.email || "No email"}
                          >
                            {partner.user?.email || "No email"}
                          </p>
                        </div>
                      </div>
                      <div className="text-right flex-shrink-0 ml-3">
                        <p className="font-bold text-green-600 dark:text-green-400 text-lg">
                          ${partner.totalRevenue || 0}
                        </p>
                        <Badge variant="secondary" className="text-xs">
                          {partner.totalConversions} sales
                        </Badge>
                      </div>
                    </div>
                  ))
              ) : (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  <svg className="h-12 w-12 mx-auto mb-2 text-gray-300 dark:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  No partners found
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Top Referrals */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center space-x-2">
                <svg className="h-5 w-5 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span>Top Referrals</span>
              </CardTitle>
              <button
                onClick={handleViewAllCustomers}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium transition-colors"
              >
                View All →
              </button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {isLoading ? (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  Loading referrals...
                </div>
              ) : dashboardStats?.topReferrals?.length > 0 ? (
                dashboardStats.topReferrals
                  .slice(0, 8)
                  .map((referral: any) => (
                    <div
                      key={referral.id}
                      className="flex items-center justify-between p-4 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer transition-all duration-200 border border-gray-100 dark:border-gray-700"
                    >
                      <div className="flex items-center space-x-3 min-w-0 flex-1">
                        <div
                          className={`w-4 h-4 rounded-full flex-shrink-0 ${
                            referral.referral_status === "conversion"
                              ? "bg-green-500"
                              : referral.referral_status === "lead"
                              ? "bg-blue-500"
                              : referral.referral_status === "cross"
                              ? "bg-purple-500"
                              : "bg-gray-500"
                          }`}
                        ></div>
                        <div className="min-w-0 flex-1">
                          <p
                            className="font-semibold text-gray-900 dark:text-gray-100 truncate"
                            title={
                              referral.user?.username ||
                              referral.user?.email ||
                              "Anonymous User"
                            }
                          >
                            {referral.user?.username ||
                              referral.user?.email ||
                              "Anonymous User"}
                          </p>
                          <p
                            className="text-sm text-gray-500 dark:text-gray-400 truncate"
                            title={referral.user?.email || "No email"}
                          >
                            {referral.user?.email || "No email"}
                          </p>
                        </div>
                      </div>
                      <div className="text-right flex-shrink-0 ml-3">
                        <p className="font-bold text-gray-900 dark:text-gray-100 text-lg">
                          ${referral.total_paid || 0}
                        </p>
                        <Badge
                          variant={
                            referral.referral_status === "conversion"
                              ? "default"
                              : referral.referral_status === "lead"
                              ? "secondary"
                              : "outline"
                          }
                          className={`text-xs ${
                            referral.referral_status === "conversion"
                              ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                              : referral.referral_status === "lead"
                              ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                              : referral.referral_status === "cross"
                              ? "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400"
                              : "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
                          }`}
                        >
                          {referral.referral_status === "conversion"
                            ? "Conversion"
                            : referral.referral_status === "lead"
                            ? "Lead"
                            : referral.referral_status === "cross"
                            ? "Cross"
                            : "Unknown"}
                        </Badge>
                      </div>
                    </div>
                  ))
              ) : (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  <svg className="h-12 w-12 mx-auto mb-2 text-gray-300 dark:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  No referrals found
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
