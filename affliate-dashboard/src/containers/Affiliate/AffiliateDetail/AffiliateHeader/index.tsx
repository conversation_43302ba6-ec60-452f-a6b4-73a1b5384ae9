import { IAffiliate } from "@/interfaces";
import _ from "lodash";
import { affiliateActions } from "@/features/rootActions";
import { useDispatch, useSelector } from "react-redux";
import {
  selectLoadingAffiliateUrl,
  selectLoadingAffiliateUrlId,
  selectLoadingAffiliateSummary,
  selectLoadingAffiliateSummaryId,
} from "@/features/affiliate/affiliate.slice";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";
import { ArrowUpRight, Tag, Globe, CalendarDays, Repeat } from 'lucide-react';

export default function AffiliateHeader({ program }: { program: IAffiliate }) {
  const dispatch = useDispatch();
  const loadingUrl = useSelector(selectLoadingAffiliateUrl);
  const loadingUrlId = useSelector(selectLoadingAffiliateUrlId);
  const loadingSummary = useSelector(selectLoadingAffiliateSummary);
  const loadingSummaryId = useSelector(selectLoadingAffiliateSummaryId);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // Check if this specific affiliate's URL/summary is being loaded
  const isLoadingUrl = loadingUrl && loadingUrlId === program.documentId;
  const isLoadingSummary =
    loadingSummary && loadingSummaryId === program.documentId;

  const handleVisitSite = () => {
    if (program.documentId) {
      dispatch(affiliateActions.fetchAffiliateUrl({ id: program.documentId }));
    }
  };

  const handleAISummary = () => {
    if (program.documentId) {
      dispatch(
        affiliateActions.fetchAffiliateSummary({ id: program.documentId })
      );
    }
  };

  const InfoTag = ({ icon, text }: { icon?: React.ReactNode; text: string | number }) => (
    <div className="inline-flex items-center gap-1.5 bg-slate-100 text-slate-700 text-xs font-semibold px-3 py-1.5 rounded-full shadow-sm border border-slate-200 dark:bg-slate-800 dark:text-slate-200 dark:border-slate-700">
      {icon && <div className="text-slate-500 dark:text-slate-400">{icon}</div>}
      <span>{text}</span>
    </div>
  );

  const VisitWebsiteButton = ({ id }: { id:string }) => (
    <button
      id={id}
      onClick={handleVisitSite}
      disabled={isLoadingUrl}
      className="inline-flex items-center bg-white rounded-full text-sm font-medium shadow-lg border-2 border-blue-500 transition-all duration-200 transform hover:scale-105 hover:shadow-xl focus:outline-none disabled:opacity-70 disabled:scale-100"
    >
      {isLoadingUrl ? (
        <div className="flex items-center justify-center w-full px-3 py-1.5">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
          <span className="ml-2 font-semibold text-blue-500">Loading...</span>
        </div>
      ) : (
        <>
          <div className="flex items-center justify-center bg-blue-500 rounded-full p-1.5 m-1">
            <ArrowUpRight className="w-3.5 h-3.5 text-white" strokeWidth={2.5}/>
          </div>
          <span className="ml-2 mr-3 font-semibold text-blue-500">Visit Website</span>
        </>
      )}
    </button>
  );

  const renderAISummaryButton = () => {
    return (
      <button
        className="inline-flex cursor-pointer whitespace-nowrap items-center justify-center gap-1 px-2 py-1 text-xs font-medium text-purple-600 bg-purple-50 hover:bg-purple-100 rounded-md transition-colors"
        onClick={handleAISummary}
      >
        {isLoadingSummary ? (
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-purple-600 border-t-transparent"></div>
        ) : (
          <svg
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-purple-600"
          >
            <path
              d="M12 1.5L15.09 7.26L21 8.27L16.5 12.14L17.18 18.02L12 15.77L6.82 18.02L7.5 12.14L3 8.27L8.91 7.26L12 1.5Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth="1"
            />
            <circle cx="12" cy="12" r="2" fill="white" />
          </svg>
        )}
        <span>{isLoadingSummary ? "Loading..." : "AI Summary"}</span>
      </button>
    );
  };

  return (
    <div className="w-full">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-start gap-4 md:gap-0">
        {/* Left side: Logo, Title and Tags */}
        <div className="flex items-start gap-4 flex-1 w-full">
          {/* Logo */}
          <div className="w-12 h-12 md:w-16 md:h-16 overflow-hidden rounded-lg flex-shrink-0 border border-slate-200 dark:border-slate-700">
            <img
              src={_.get(program.image, "url", "https://example.com/default-image.jpg")}
              alt={program.name}
              className="w-full h-full object-contain bg-white dark:bg-slate-800"
            />
          </div>
          
          {/* Title and Tags */}
          <div className="flex flex-col gap-3 flex-1 min-w-0">
            <div className="flex items-center gap-3 flex-wrap">
              <h1 className="text-2xl md:text-4xl font-bold text-slate-800 dark:text-white truncate">
                {program.name}
              </h1>
              {renderAISummaryButton()}
            </div>
            <div className="flex items-center gap-3 flex-wrap">
              {program.categories?.[0]?.name && (
                <InfoTag icon={<Tag size={14} />} text={program.categories[0].name} />
              )}
              {program.country && (
                <InfoTag icon={<Globe size={14} />} text={program.country} />
              )}
              {program.launch_year && (
                <InfoTag icon={<CalendarDays size={14} />} text={program.launch_year} />
              )}
              {program.recurring === 'TRUE' && (
                 <InfoTag icon={<Repeat size={14} className="text-green-500"/>} text="Recurring" />
              )}
            </div>
          </div>
        </div>

        {/* Right side: Visit Button (desktop), Below content (mobile) */}
        <div className="hidden md:flex flex-shrink-0">
          <VisitWebsiteButton id="visit-page-desktop" />
        </div>
      </div>
      {/* Mobile Visit Button */}
      <div className="flex md:hidden mt-4 w-full">
        <VisitWebsiteButton id="visit-page-mobile" />
      </div>
    </div>
  );
}