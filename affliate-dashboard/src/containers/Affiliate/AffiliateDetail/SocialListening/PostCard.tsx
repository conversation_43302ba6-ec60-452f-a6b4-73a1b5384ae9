import React, { useState, useRef, useEffect } from "react";
import {
  EyeIcon,
  MessageCircle,
  ThumbsUp,
  LogIn,
  ExternalLink,
} from "lucide-react";
import { NumberFormat } from "@/components/NumberFormat";
import { IVideo } from "@/interfaces";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useDispatch, useSelector } from "react-redux";
import { actions } from "@/features/social-listening/social-listening.slice";
import {
  selectLoadingTranscriptId,
  selectTranscript,
  selectSocialListeningCurrent,
} from "@/features/selectors";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";
import Link from "next/link";
import Thumbnail from "@/components/thumbnail";
import he from "he";

export default function PostCard({ post }: { post: IVideo | null }) {
  const dispatch = useDispatch();
  const loadingTranscriptId = useSelector(selectLoadingTranscriptId);
  const transcript = useSelector(selectTranscript);
  const currentPost = useSelector(selectSocialListeningCurrent);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const [showAuthPopover, setShowAuthPopover] = useState(false);
  const aiButtonRef = useRef<HTMLButtonElement>(null);
  const [authPopoverPosition, setAuthPopoverPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);

  // Determine if this specific post's transcript is loading
  const isThisPostLoading =
    post?.video_id &&
    ((currentPost?.video_id === post.video_id && loadingTranscriptId) ||
      loadingTranscriptId === post.video_id);

  const handleAuthPopoverChange = (open: boolean) => {
    // Only allow opening if user is not authenticated
    // Always allow closing the popover
    if (!open || !isAuthenticated) {
      setShowAuthPopover(open);
    }
  };

  const handleAISnippetClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    // Check if user is authenticated
    if (!isAuthenticated) {
      // Use the mouse click position for positioning the popover
      const clickX = e.clientX;
      const clickY = e.clientY;

      // Position the popover near the click point
      setAuthPopoverPosition({
        x: clickX,
        y: clickY + 20, // Position 20px below the click
      });

      // Open auth popover
      setShowAuthPopover(true);
      return;
    }

    if (!post || !post.video_id) {
      console.error("Video ID not found");
      return;
    }

    // Set current post first
    dispatch(actions.setSocialListening(post));

    dispatch(actions.fetchTranscript(post.video_id));
    console.log("Fetching transcript...");
  };

  // Function to open AIScript with transcript
  const openAIScript = (transcriptText: string) => {
    // Clear any previous messages
  };

  // Watch for changes in the transcript state
  useEffect(() => {
    // Only react if this is the post that was being loaded
    // Check if current post matches this post AND we have a transcript
    if (
      transcript &&
      currentPost?.video_id === post?.video_id &&
      !loadingTranscriptId
    ) {
      console.log("Opening AIScript with transcript for current post");
      openAIScript(transcript);
    }
  }, [transcript, currentPost?.video_id, post?.video_id, loadingTranscriptId]);

  if (!post) return null;

  // Platform icon mapping function
  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "youtube":
        return (
          <svg
            viewBox="0 0 461.001 461.001"
            className="w-5 h-5"
            aria-hidden="true"
          >
            <g>
              <path
                style={{ fill: "#F61C0D" }}
                d="M365.257,67.393H95.744C42.866,67.393,0,110.259,0,163.137v134.728
                c0,52.878,42.866,95.744,95.744,95.744h269.513c52.878,0,95.744-42.866,95.744-95.744V163.137
                C461.001,110.259,418.135,67.393,365.257,67.393z M300.506,237.056l-126.06,60.123c-3.359,1.602-7.239-0.847-7.239-4.568V168.607
                c0-3.774,3.982-6.22,7.348-4.514l126.06,63.881C304.363,229.873,304.298,235.248,300.506,237.056z"
              />
            </g>
          </svg>
        );
      case "tiktok":
        return (
          <svg
            viewBox="0 0 32 32"
            className="w-5 h-5"
            aria-hidden="true"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8.45095 19.7926C8.60723 18.4987 9.1379 17.7743 10.1379 17.0317C11.5688 16.0259 13.3561 16.5948 13.3561 16.5948V13.2197C13.7907 13.2085 14.2254 13.2343 14.6551 13.2966V17.6401C14.6551 17.6401 12.8683 17.0712 11.4375 18.0775C10.438 18.8196 9.90623 19.5446 9.7505 20.8385C9.74562 21.5411 9.87747 22.4595 10.4847 23.2536C10.3345 23.1766 10.1815 23.0889 10.0256 22.9905C8.68807 22.0923 8.44444 20.7449 8.45095 19.7926ZM22.0352 6.97898C21.0509 5.90039 20.6786 4.81139 20.5441 4.04639H21.7823C21.7823 4.04639 21.5354 6.05224 23.3347 8.02482L23.3597 8.05134C22.8747 7.7463 22.43 7.38624 22.0352 6.97898ZM28 10.0369V14.293C28 14.293 26.42 14.2312 25.2507 13.9337C23.6179 13.5176 22.5685 12.8795 22.5685 12.8795C22.5685 12.8795 21.8436 12.4245 21.785 12.3928V21.1817C21.785 21.6711 21.651 22.8932 21.2424 23.9125C20.709 25.246 19.8859 26.1212 19.7345 26.3001C19.7345 26.3001 18.7334 27.4832 16.9672 28.28C15.3752 28.9987 13.9774 28.9805 13.5596 28.9987C13.5596 28.9987 11.1434 29.0944 8.96915 27.6814C8.49898 27.3699 8.06011 27.0172 7.6582 26.6277L7.66906 26.6355C9.84383 28.0485 12.2595 27.9528 12.2595 27.9528C12.6779 27.9346 14.0756 27.9528 15.6671 27.2341C17.4317 26.4374 18.4344 25.2543 18.4344 25.2543C18.5842 25.0754 19.4111 24.2001 19.9423 22.8662C20.3498 21.8474 20.4849 20.6247 20.4849 20.1354V11.3475C20.5435 11.3797 21.2679 11.8347 21.2679 11.8347C21.2679 11.8347 22.3179 12.4734 23.9506 12.8889C25.1204 13.1864 26.7 13.2483 26.7 13.2483V9.91314C27.2404 10.0343 27.7011 10.0671 28 10.0369Z"
              fill="#EE1D52"
            />
            <path
              d="M26.7009 9.91314V13.2472C26.7009 13.2472 25.1213 13.1853 23.9515 12.8879C22.3188 12.4718 21.2688 11.8337 21.2688 11.8337C21.2688 11.8337 20.5444 11.3787 20.4858 11.3464V20.1364C20.4858 20.6258 20.3518 21.8484 19.9432 22.8672C19.4098 24.2012 18.5867 25.0764 18.4353 25.2553C18.4353 25.2553 17.4337 26.4384 15.668 27.2352C14.0765 27.9539 12.6788 27.9357 12.2604 27.9539C12.2604 27.9539 9.84473 28.0496 7.66995 26.6366L7.6591 26.6288C7.42949 26.4064 7.21336 26.1717 7.01177 25.9257C6.31777 25.0795 5.89237 24.0789 5.78547 23.7934C5.78529 23.7922 5.78529 23.791 5.78547 23.7898C5.61347 23.2937 5.25209 22.1022 5.30147 20.9482C5.38883 18.9122 6.10507 17.6625 6.29444 17.3494C6.79597 16.4957 7.44828 15.7318 8.22233 15.0919C8.90538 14.5396 9.6796 14.1002 10.5132 13.7917C11.4144 13.4295 12.3794 13.2353 13.3565 13.2197V16.5948C13.3565 16.5948 11.5691 16.028 10.1388 17.0317C9.13879 17.7743 8.60812 18.4987 8.45185 19.7926C8.44534 20.7449 8.68897 22.0923 10.0254 22.991C10.1813 23.0898 10.3343 23.1775 10.4845 23.2541C10.7179 23.5576 11.0021 23.8221 11.3255 24.0368C12.631 24.8632 13.7249 24.9209 15.1238 24.3842C16.0565 24.0254 16.7586 23.2167 17.0842 22.3206C17.2888 21.7611 17.2861 21.1978 17.2861 20.6154V4.04639H20.5417C20.6763 4.81139 21.0485 5.90039 22.0328 6.97898C22.4276 7.38624 22.8724 7.7463 23.3573 8.05134C23.5006 8.19955 24.2331 8.93231 25.1734 9.38216C25.6596 9.61469 26.1722 9.79285 26.7009 9.91314Z"
              fill="#000000"
            />
            <path
              d="M4.48926 22.7568V22.7594L4.57004 22.9784C4.56076 22.9529 4.53074 22.8754 4.48926 22.7568Z"
              fill="#69C9D0"
            />
            <path
              d="M10.5128 13.7916C9.67919 14.1002 8.90498 14.5396 8.22192 15.0918C7.44763 15.7332 6.79548 16.4987 6.29458 17.354C6.10521 17.6661 5.38897 18.9168 5.30161 20.9528C5.25223 22.1068 5.61361 23.2983 5.78561 23.7944C5.78543 23.7956 5.78543 23.7968 5.78561 23.798C5.89413 24.081 6.31791 25.0815 7.01191 25.9303C7.2135 26.1763 7.42963 26.4111 7.65924 26.6334C6.92357 26.1457 6.26746 25.5562 5.71236 24.8839C5.02433 24.0451 4.60001 23.0549 4.48932 22.7626C4.48919 22.7605 4.48919 22.7584 4.48932 22.7564V22.7527C4.31677 22.2571 3.95431 21.0651 4.00477 19.9096C4.09213 17.8736 4.80838 16.6239 4.99775 16.3108C5.4985 15.4553 6.15067 14.6898 6.92509 14.0486C7.608 13.4961 8.38225 13.0567 9.21598 12.7484C9.73602 12.5416 10.2778 12.3891 10.8319 12.2934C11.6669 12.1537 12.5198 12.1415 13.3588 12.2575V13.2196C12.3808 13.2349 11.4148 13.4291 10.5128 13.7916Z"
              fill="#69C9D0"
            />
            <path
              d="M20.5438 4.04635H17.2881V20.6159C17.2881 21.1983 17.2881 21.76 17.0863 22.3211C16.7575 23.2167 16.058 24.0253 15.1258 24.3842C13.7265 24.923 12.6326 24.8632 11.3276 24.0368C11.0036 23.823 10.7187 23.5594 10.4844 23.2567C11.5962 23.8251 12.5913 23.8152 13.8241 23.341C14.7558 22.9821 15.4563 22.1734 15.784 21.2774C15.9891 20.7178 15.9864 20.1546 15.9864 19.5726V3H20.4819C20.4819 3 20.4315 3.41188 20.5438 4.04635ZM26.7002 8.99104V9.9131C26.1725 9.79263 25.6609 9.61447 25.1755 9.38213C24.2352 8.93228 23.5026 8.19952 23.3594 8.0513C23.5256 8.1559 23.6981 8.25106 23.8759 8.33629C25.0192 8.88339 26.1451 9.04669 26.7002 8.99104Z"
              fill="#69C9D0"
            />
          </svg>
        );
      default:
        // Fall back to text for other platforms
        const platformName = platform.charAt(0).toUpperCase();
        return (
          <div className="flex items-center justify-center w-5 h-5 rounded-full bg-gray-200 text-gray-700 font-bold">
            {platformName}
          </div>
        );
    }
  };

  // Create a function to render the AI Script button consistently
  const renderAIScriptButton = () => {
    return (
      <button
        ref={aiButtonRef}
        className="inline-flex cursor-pointer whitespace-nowrap items-center justify-center gap-1 px-2 py-1 text-xs font-medium text-purple-600 bg-purple-50 hover:bg-purple-100 rounded-md transition-colors"
        onClick={handleAISnippetClick}
      >
        {isThisPostLoading ? (
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-purple-600 border-t-transparent"></div>
        ) : (
          <svg
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-purple-600"
          >
            <path
              d="M12 1.5L15.09 7.26L21 8.27L16.5 12.14L17.18 18.02L12 15.77L6.82 18.02L7.5 12.14L3 8.27L8.91 7.26L12 1.5Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth="1"
            />
            <circle cx="12" cy="12" r="2" fill="white" />
          </svg>
        )}
        <span>{isThisPostLoading ? "Loading..." : "AI Script+"}</span>
      </button>
    );
  };

  return (
    <div className="relative w-full">
      {/* Authentication popover with click-based positioning */}
      <Popover open={showAuthPopover} onOpenChange={handleAuthPopoverChange}>
        <PopoverTrigger className="hidden">
          <span />
        </PopoverTrigger>
        <PopoverContent
          className="w-72 p-4"
          side="bottom"
          align="center"
          style={
            authPopoverPosition
              ? {
                  position: "fixed",
                  top: `${authPopoverPosition.y}px`,
                  left:
                    Math.max(
                      10,
                      Math.min(
                        window.innerWidth - 298,
                        authPopoverPosition.x - 144
                      )
                    ) + "px",
                  transform: "none",
                  zIndex: 9999,
                  maxWidth: "calc(100vw - 20px)",
                  width:
                    window.innerWidth <= 298
                      ? `${window.innerWidth - 20}px`
                      : "288px",
                }
              : undefined
          }
        >
          <div className="space-y-3">
            <div className="font-medium text-center flex items-center justify-center gap-2">
              <LogIn size={16} className="text-purple-600" />
              <span>Authentication Required</span>
            </div>
            <p className="text-sm text-muted-foreground">
              You need to be logged in to use the AI script feature. This tool
              analyzes video content to help you create engaging affiliate
              content.
            </p>
            <div className="flex justify-center pt-2">
              <Link
                href={`/authentication?redirect=${encodeURIComponent(
                  window.location.pathname
                )}`}
                className="inline-flex items-center justify-center rounded-md bg-purple-600 px-4 py-2 text-xs font-medium text-white hover:bg-purple-700"
              >
                Login to continue
              </Link>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Main container with a table-like layout */}
      <div className="flex w-full items-center gap-4 py-2 border-b border-slate-100 dark:border-slate-800">
        {/* Thumbnail and Platform Icon */}
        <div className="flex-shrink-0 w-28 flex items-center justify-center">
          <div
            className={`relative w-[100px] ${
              post.platform === "tiktok" ? "h-[100px]" : "h-[70px]"
            } flex items-center justify-center`}
          >
            <Thumbnail
              video={post}
              size={{
                width: "100px",
                height: post.platform === "tiktok" ? "100px" : "70px",
              }}
            />
            <div className="absolute bottom-1 right-1">
              {getPlatformIcon(post.platform)}
            </div>
          </div>
        </div>

        {/* Title and AI Script Button */}
        <div className="flex-1 min-w-0">
          <div
            className="line-clamp-3 font-medium text-slate-800 dark:text-white"
            title={he.decode(post.title)}
            style={{ fontSize: "13.5px" }}
          >
            {he.decode(post.title)}
          </div>
          <div className="mt-1">{renderAIScriptButton()}</div>
        </div>

        {/* Stats Section */}
        <div className="flex flex-shrink-0 items-center gap-4 text-xs text-slate-500 dark:text-white">
          <div className="flex items-center justify-center gap-1.5 w-16">
            <EyeIcon size={14} />
            <NumberFormat value={post.views} />
          </div>
          <div className="flex items-center justify-center gap-1.5 w-16">
            <ThumbsUp size={14} />
            <NumberFormat value={post.likes} />
          </div>
          <div className="flex items-center justify-center gap-1.5 w-16">
            <MessageCircle size={14} />
            <span>{post.comments}</span>
          </div>
          <div className="w-20 text-center text-xs">
            {post.published_from &&
              new Date(post.published_from).toLocaleDateString("en-US", {
                year: "2-digit",
                month: "numeric",
                day: "numeric",
              })}
          </div>
          <div className="w-8 flex justify-center">
            <a
              href={post.video_link}
              target="_blank"
              rel="noopener noreferrer"
              className="text-slate-400 hover:text-slate-600 dark:text-white dark:hover:text-slate-300"
              title="View on platform"
            >
              <ExternalLink size={16} />
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}