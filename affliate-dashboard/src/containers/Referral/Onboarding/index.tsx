import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useDispatch, useSelector } from "react-redux";
import {
  User,
  MapPin,
  CreditCard,
  CheckCircle,
  ArrowLeft,
  Loader,
  AlertCircle,
} from "lucide-react";
import {
  selectUserData,
  selectUserLoading,
  selectUserError,
  selectUserIsUpdating,
  selectReferrerLoading,
  selectReferrerError,
  selectReferrerSuccess,
} from "@/features/selectors";
import { actions as userActions } from "@/features/user/user.slice";
import { referrerActions } from "@/features/rootActions";
// Remove StrapiClient import as we'll use Redux instead
// import { StrapiClient } from "@/utils/request";

// Import ConfirmModal component
import ConfirmModal from "./Payment/ConfirmModal";

// Import step components
import ProfileStep, { ProfileFormData } from "./Profile";
import AddressStep, { AddressFormData } from "./Address";
import PaymentStep, { PaymentFormData } from "./Payment";

// Define a type for tracking what kind of update is in progress
type UpdateType = "profile" | "address" | "final" | null;

// Main component
const OnboardingContainer: React.FC = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const userData = useSelector(selectUserData);
  const isLoading = useSelector(selectUserLoading);
  const isUpdating = useSelector(selectUserIsUpdating);
  const userError = useSelector(selectUserError);
  // Add referrer selectors
  const isRegisteringReferrer = useSelector(selectReferrerLoading);
  const referrerError = useSelector(selectReferrerError);
  const referrerSuccess = useSelector(selectReferrerSuccess);
  const [redirecting, setRedirecting] = useState(false);

  // Track what type of update is currently in progress
  const [currentUpdateType, setCurrentUpdateType] = useState<UpdateType>(null);

  // Fetch user data when component mounts
  useEffect(() => {
    dispatch(userActions.fetchUserMe());
  }, []);

  // Redirect if userData doesn't exist (and we're no longer loading)
  useEffect(() => {
    if (!isLoading && !userData) {
      console.log("No user data found, redirecting to authentication");
      setRedirecting(true);
      const timer = setTimeout(() => {
        router.replace("/authentication?redirect=/affiliate/onboarding");
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [userData, isLoading, router]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isProfileUpdating, setIsProfileUpdating] = useState(false);
  const [isAddressUpdating, setIsAddressUpdating] = useState(false);

  // Handle update completion based on isUpdating state
  useEffect(() => {
    // Only handle completion when isUpdating transitions from true to false
    // and we have a current update type
    if (!isUpdating && currentUpdateType) {
      // Handle based on update type
      if (currentUpdateType === "profile") {
        setIsProfileUpdating(false);
        if (!userError) {
          setCurrentStep(2);
          setErrors({});
        }
      } else if (currentUpdateType === "address") {
        setIsAddressUpdating(false);
        if (!userError) {
          setCurrentStep(3);
          setErrors({});
        }
      } else if (currentUpdateType === "final") {
        setIsSubmitting(false);
        if (!userError) {
          router.push("/affiliate#dashboard");
        }
      }

      // Reset current update type
      setCurrentUpdateType(null);
    }
  }, [isUpdating, currentUpdateType, userError]);

  // Set submission error from Redux if present during update
  useEffect(() => {
    if (userError && (isProfileUpdating || isAddressUpdating || isSubmitting)) {
      setErrors({
        submission: userError,
      });

      // Reset loading states if we have an error
      setIsProfileUpdating(false);
      setIsAddressUpdating(false);
      setIsSubmitting(false);
      setCurrentUpdateType(null);
    }
  }, [userError, isProfileUpdating, isAddressUpdating, isSubmitting]);

  // Form validation state
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Form validity states
  const [isProfileValid, setIsProfileValid] = useState(false);
  const [isAddressValid, setIsAddressValid] = useState(true); // Address is optional
  const [isPaymentValid, setIsPaymentValid] = useState(false);

  // Current step state
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  // Form data states
  const [profileData, setProfileData] = useState<ProfileFormData>({
    email: "",
    firstName: "",
    lastName: "",
  });

  const [addressData, setAddressData] = useState<AddressFormData>({
    streetAddress: "",
    city: "",
    state: "",
    zipCode: "",
    country: "",
  });

  const [paymentData, setPaymentData] = useState<PaymentFormData>({
    method: "paypal",
    paypal: { paypalEmail: "" },
    bankTransfer: {
      firstName: "",
      lastName: "",
      businessName: "",
      country: "",
      city: "",
      state: "",
      address: "",
      zipCode: "",
      accountNumber: "",
      swiftCode: "",
    },
  });

  // Load user data when component mounts
  useEffect(() => {
    if (userData) {
      setProfileData({
        email: userData.email || "",
        firstName: userData.first_name || "",
        lastName: userData.last_name || "",
      });

      // Also populate address data if available
      if (
        userData.address ||
        userData.city ||
        userData.state ||
        userData.country ||
        userData.zip_code
      ) {
        setAddressData({
          streetAddress: userData.address || "",
          city: userData.city || "",
          state: userData.state || "",
          zipCode: userData.zip_code || "",
          country: userData.country || "",
        });
      }

      // Populate payment data if available
      if (userData.paypal_email) {
        setPaymentData({
          ...paymentData,
          method: "paypal",
          paypal: {
            paypalEmail: userData.paypal_email || "",
          },
        });
      } else if (userData.bank_transfer) {
        const bankData = userData.bank_transfer;
        setPaymentData({
          ...paymentData,
          method: "bank-transfer",
          bankTransfer: {
            firstName: bankData.first_name || "",
            lastName: bankData.last_name || "",
            businessName: bankData.business_name || "",
            country: bankData.country || "",
            city: bankData.city || "",
            state: bankData.state || "",
            address: bankData.address || "",
            zipCode: bankData.zip_code || "",
            accountNumber: bankData.account_number || "",
            swiftCode: bankData.swift_code || "",
          },
        });
      }

      // Validate profile after loading user data
      validateProfileData(userData.first_name || "", userData.last_name || "");
    }
  }, [userData]);

  const validateProfileData = (
    firstName = profileData.firstName,
    lastName = profileData.lastName
  ) => {
    const newErrors: { [key: string]: string } = {};

    if (!firstName.trim()) {
      newErrors.firstName = "First name is required";
    }

    if (!lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }

    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    setIsProfileValid(isValid);
    return isValid;
  };

  const validateAddressData = () => {
    // If all fields are empty, consider it valid as it's optional
    const isEmpty =
      !addressData.streetAddress &&
      !addressData.city &&
      !addressData.state &&
      !addressData.country &&
      !addressData.zipCode;

    if (isEmpty) {
      setErrors({});
      setIsAddressValid(true);
      return true;
    }

    // Otherwise, validate normally
    const newErrors: { [key: string]: string } = {};

    if (!addressData.streetAddress.trim()) {
      newErrors.streetAddress = "Street address is required";
    }

    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    setIsAddressValid(isValid);
    return isValid;
  };

  const validatePaymentData = (
    method = paymentData.method,
    paypalEmail = paymentData.paypal?.paypalEmail,
    bankData = paymentData.bankTransfer
  ) => {
    // If method is empty or no data provided, consider it valid (optional)
    if (!method) {
      setErrors({});
      setIsPaymentValid(true);
      return true;
    }

    // If method is provided, validate accordingly
    const newErrors: { [key: string]: string } = {};

    if (method === "paypal") {
      if (!paypalEmail) {
        newErrors.paypalEmail = "PayPal email is required";
      } else if (!/^\S+@\S+\.\S+$/.test(paypalEmail)) {
        newErrors.paypalEmail = "Please enter a valid email address";
      }
    } else if (method === "bank-transfer") {
      if (!bankData?.accountNumber) {
        newErrors.accountNumber = "Account number is required";
      }
      if (!bankData?.firstName) {
        newErrors.bankFirstName = "First name is required";
      }
      if (!bankData?.lastName) {
        newErrors.bankLastName = "Last name is required";
      }
    }

    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    setIsPaymentValid(isValid);
    return isValid;
  };

  const handleProfileSubmit = () => {
    if (validateProfileData()) {
      // Check if data has actually changed before making an API call
      if (
        profileData.firstName !== (userData?.first_name || "") ||
        profileData.lastName !== (userData?.last_name || "")
      ) {
        // Set update in progress
        setIsProfileUpdating(true);
        setCurrentUpdateType("profile");

        try {
          // Format just the profile data for immediate update
          const profileUpdateData = {
            first_name: profileData.firstName,
            last_name: profileData.lastName,
          };

          // Dispatch action to update user profile
          dispatch(userActions.updateUserProfile(profileUpdateData));
        } catch (error: any) {
          console.error("Error updating profile data:", error);
          setErrors({
            submission:
              "Failed to save your profile information. Please try again.",
          });
          setIsProfileUpdating(false);
          setCurrentUpdateType(null);
        }
      } else {
        // No changes detected, just move to the next step
        setCurrentStep(2);
        setErrors({});
      }
    }
  };

  const handleAddressSubmit = () => {
    if (validateAddressData()) {
      // Only make API call if any address data has changed
      const hasAddressChanged =
        addressData.streetAddress !== (userData?.address || "") ||
        addressData.city !== (userData?.city || "") ||
        addressData.state !== (userData?.state || "") ||
        addressData.country !== (userData?.country || "") ||
        addressData.zipCode !== (userData?.zip_code || "");

      if (hasAddressChanged) {
        // Set update in progress
        setIsAddressUpdating(true);
        setCurrentUpdateType("address");

        try {
          // Format address data for immediate update
          const addressUpdateData = {
            address: addressData.streetAddress,
            city: addressData.city,
            state: addressData.state,
            country: addressData.country,
            zip_code: addressData.zipCode,
          };

          // Dispatch action to update user profile with address
          dispatch(userActions.updateUserProfile(addressUpdateData));
        } catch (error: any) {
          console.error("Error updating address data:", error);
          setErrors({
            submission:
              "Failed to save your address information. Please try again.",
          });
          setIsAddressUpdating(false);
          setCurrentUpdateType(null);
        }
      } else {
        // No changes detected, just move to the next step
        setCurrentStep(3);
        setErrors({});
      }
    }
  };

  // Add state for confirm modal
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  // Remove duplicate state declarations since we're using Redux selectors
  // const [isRegisteringReferrer, setIsRegisteringReferrer] = useState(false);
  // const [referrerError, setReferrerError] = useState<string | null>(null);

  // Function to check if payment info is filled
  const isPaymentInfoFilled = () => {
    if (paymentData.method === "paypal") {
      return !!paymentData.paypal?.paypalEmail;
    } else if (paymentData.method === "bank-transfer") {
      const bankData = paymentData.bankTransfer;
      return !!(
        bankData?.accountNumber &&
        bankData?.firstName &&
        bankData?.lastName
      );
    }
    return false;
  };

  // Function to register user as referrer
  const registerAsReferrer = async () => {
    // Use Redux action instead of direct API call
    dispatch(referrerActions.registerReferrer());
  };

  // Function to handle confirm modal
  const handleConfirmWithoutPayment = () => {
    setIsConfirmModalOpen(false);
    dispatch(referrerActions.registerReferrer());
  };

  // Handle referrer registration success
  useEffect(() => {
    if (referrerSuccess) {
      // Redirect to dashboard on success
      router.push("/affiliate#dashboard");
    }
  }, [referrerSuccess, router]);

  // Update the final submit handler
  const handleFinalSubmit = async () => {
    // Check if payment info is filled
    if (isPaymentInfoFilled()) {
      if (validatePaymentData()) {
        // Set update in progress
        setIsSubmitting(true);
        setCurrentUpdateType("final");

        try {
          // Format data according to API requirements
          const formattedData: any = {
            first_name: profileData.firstName,
            last_name: profileData.lastName,
            // Include address data if it was filled
            ...(addressData.streetAddress && {
              address: addressData.streetAddress,
              city: addressData.city,
              state: addressData.state,
              country: addressData.country,
              zip_code: addressData.zipCode,
            }),
          };

          // Add payment method specific data only if method is selected
          if (
            paymentData.method === "paypal" &&
            paymentData.paypal?.paypalEmail
          ) {
            formattedData.paypal_email = paymentData.paypal.paypalEmail;
          } else if (
            paymentData.method === "bank-transfer" &&
            paymentData.bankTransfer?.accountNumber
          ) {
            formattedData.bank_transfer = {
              account_number: paymentData.bankTransfer.accountNumber,
              swift_code: paymentData.bankTransfer.swiftCode,
              first_name: paymentData.bankTransfer.firstName,
              last_name: paymentData.bankTransfer.lastName,
              business_name: paymentData.bankTransfer.businessName,
              country: paymentData.bankTransfer.country,
              city: paymentData.bankTransfer.city,
              state: paymentData.bankTransfer.state,
              address: paymentData.bankTransfer.address,
              zip_code: paymentData.bankTransfer.zipCode,
            };
          }

          // Dispatch action to update user profile
          await dispatch(userActions.updateUserProfile(formattedData));

          // Now register as referrer using Redux action
          dispatch(referrerActions.registerReferrer());
        } catch (error: any) {
          console.error("Error saving affiliate data:", error);
          setErrors({
            submission: "Failed to save your information. Please try again.",
          });
          setIsSubmitting(false);
          setCurrentUpdateType(null);
        }
      }
    } else {
      // Payment info not filled, show confirmation modal
      setIsConfirmModalOpen(true);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      // Clear errors when moving back
      setErrors({});
    }
  };

  const handleProfileChange = (newData: ProfileFormData) => {
    setProfileData(newData);
    validateProfileData(newData.firstName, newData.lastName);
  };

  const handleAddressChange = (newData: AddressFormData) => {
    setAddressData(newData);
    validateAddressData();

    // Validate street address when data changes
    const newErrors: { [key: string]: string } = {};
    if (!newData.streetAddress.trim()) {
      newErrors.streetAddress = "Street address is required";
    }

    setErrors(newErrors);
    setIsAddressValid(Object.keys(newErrors).length === 0);
  };

  const handlePaymentChange = (newData: PaymentFormData) => {
    setPaymentData(newData);
    validatePaymentData(
      newData.method,
      newData.paypal?.paypalEmail,
      newData.bankTransfer
    );
  };

  // Handle skipping steps
  const handleSkipAddress = () => {
    // Move to next step without validation or saving address
    setCurrentStep(3);
    setErrors({});
  };

  // Step rendering functions
  const renderStepper = () => {
    const steps = [
      {
        id: 1,
        name: "Profile",
        description: "Your personal information",
        isCompleted: currentStep > 1,
        icon: <User className="w-5 h-5" />,
      },
      {
        id: 2,
        name: "Address",
        description: "Where you're located",
        isCompleted: currentStep > 2,
        icon: <MapPin className="w-5 h-5" />,
      },
      {
        id: 3,
        name: "Payment",
        description: "How you get paid",
        isCompleted: false,
        icon: <CreditCard className="w-5 h-5" />,
      },
    ];

    return (
      <div className="w-full mb-8">
        <div className="text-center mb-6">
          <h1 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
            Step {currentStep} of {totalSteps} • {steps[currentStep - 1].name}{" "}
            Information
          </h1>
        </div>

        <div className="relative max-w-3xl mx-auto px-4 mt-12">
          {/* Progress bar - thin horizontal line */}
          <div className="h-0.5 bg-gray-300 absolute top-6 left-0 right-0 z-0"></div>
          <div
            className="h-0.5 bg-green-500 absolute top-6 left-0 z-0 transition-all duration-300"
            style={{
              width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%`,
            }}
          ></div>

          {/* Step indicators */}
          <div className="flex justify-between relative z-10">
            {steps.map((step) => (
              <div
                key={step.id}
                className="flex flex-col items-center cursor-pointer"
                onClick={() => {
                  // Allow clicking on completed steps to navigate back
                  if (step.id < currentStep) {
                    setCurrentStep(step.id);
                    setErrors({});
                  }
                }}
              >
                <div
                  className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300
                  ${
                    step.id === currentStep
                      ? "bg-green-500 text-white ring-2 ring-green-500 ring-offset-2 shadow-md"
                      : step.isCompleted
                      ? "bg-green-500 text-white"
                      : "bg-gray-300 text-gray-700"
                  }`}
                >
                  {step.isCompleted ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    step.icon
                  )}
                </div>
                <span
                  className={`mt-2 font-medium text-sm ${
                    step.id === currentStep
                      ? "text-green-600"
                      : step.isCompleted
                      ? "text-green-600"
                      : "text-primary-foreground"
                  }`}
                >
                  {step.name}
                </span>
                <span className="text-xs text-primary-foreground mt-1">
                  {step.description}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Handle authentication required state
  if (!userData && !isLoading) {
    return (
      <div className="max-w-6xl mx-auto p-6 flex flex-col items-center justify-center min-h-[60vh]">
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6 max-w-md w-full text-center">
          <div className="flex justify-center mb-4">
            <AlertCircle className="h-12 w-12 text-red-500" />
          </div>
          <h2 className="text-xl font-semibold mb-2 dark:text-white">
            Authentication Required
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Please sign in to access the affiliate onboarding process.
          </p>
          {redirecting ? (
            <p className="text-sm text-blue-600 dark:text-blue-400">
              Redirecting to sign in...
            </p>
          ) : (
            <button
              onClick={() =>
                router.replace("/authentication?redirect=/affiliate/onboarding")
              }
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
            >
              Sign In
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="py-10">
      <div className="max-w-3xl mx-auto mb-6 px-4">
        <button
          onClick={() => router.push("/affiliate")}
          className="flex items-center text-sm font-medium text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          Back to Affiliate Dashboard
        </button>
      </div>

      {renderStepper()}

      {isLoading && (
        <div className="max-w-3xl mx-auto px-4 mb-6">
          <div className="bg-blue-50 border border-blue-100 rounded-md p-4 flex items-center">
            <Loader className="w-5 h-5 text-blue-500 animate-spin mr-3" />
            <p className="text-blue-700">Loading your information...</p>
          </div>
        </div>
      )}

      <div className="mt-8">
        {currentStep === 1 && (
          <ProfileStep
            data={profileData}
            onChange={handleProfileChange}
            onNext={handleProfileSubmit}
            errors={errors}
            isValid={isProfileValid}
            isSubmitting={isProfileUpdating}
            isLoading={isLoading}
          />
        )}
        {currentStep === 2 && (
          <AddressStep
            data={addressData}
            onChange={handleAddressChange}
            onNext={handleAddressSubmit}
            onBack={handleBack}
            onSkip={handleSkipAddress}
            errors={errors}
            isValid={isAddressValid}
            isSubmitting={isAddressUpdating}
            isLoading={isLoading}
          />
        )}
        {currentStep === 3 && (
          <>
            <PaymentStep
              data={paymentData}
              onChange={handlePaymentChange}
              onComplete={handleFinalSubmit}
              onBack={handleBack}
              isSubmitting={isSubmitting || isRegisteringReferrer}
              errors={errors}
              isValid={isPaymentValid}
              isLoading={isLoading}
            />
            <ConfirmModal
              isOpen={isConfirmModalOpen}
              onClose={() => setIsConfirmModalOpen(false)}
              onConfirm={handleConfirmWithoutPayment}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default OnboardingContainer;
