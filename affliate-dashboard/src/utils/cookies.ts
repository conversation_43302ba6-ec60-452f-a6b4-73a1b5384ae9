// First run: npm i --save-dev @types/js-cookie
import Cookies from "js-cookie";

// Constants
export const REFERRAL_COOKIE_NAME = "referral_code";
export const REFERRAL_URL_COOKIE_NAME = "referral_url";
export const REFERRAL_SHORT_LINK_COOKIE_NAME = "referral_short_link";
const COOKIE_EXPIRY_DAYS = 30; // <PERSON>ie will expire after 30 days

/**
 * Set a referral code cookie
 */
export const setReferralCookie = (referralCode: string): void => {
  if (!referralCode) return;

  // Set cookie with 30-day expiration
  Cookies.set(REFERRAL_COOKIE_NAME, referralCode, {
    expires: COOKIE_EXPIRY_DAYS,
    path: "/",
    sameSite: "lax",
  });

  console.log(`Referral cookie set: ${referralCode}`);
  // Log all cookies after setting to verify
  if (typeof document !== "undefined") {
    console.log(`All cookies after setting: ${document.cookie}`);
  }
};

/**
 * Set a referral URL cookie
 */
export const setReferralUrlCookie = (sourceUrl: string): void => {
  if (!sourceUrl) return;

  // Set cookie with 30-day expiration
  Cookies.set(REFERRAL_URL_COOKIE_NAME, sourceUrl, {
    expires: COOKIE_EXPIRY_DAYS,
    path: "/",
    sameSite: "lax",
  });

  console.log(`Referral URL cookie set: ${sourceUrl}`);
};

/**
 * Set a referral short link cookie
 */
export const setReferralShortLinkCookie = (shortLink: string): void => {
  if (!shortLink) return;

  // Set cookie with 30-day expiration
  Cookies.set(REFERRAL_SHORT_LINK_COOKIE_NAME, shortLink, {
    expires: COOKIE_EXPIRY_DAYS,
    path: "/",
    sameSite: "lax",
  });

  console.log(`Referral short link cookie set: ${shortLink}`);
};

/**
 * Fallback method to read cookie directly from document.cookie
 */
const getReferralCookieFallback = (): string | undefined => {
  if (typeof document === "undefined") return undefined;

  const cookies = document.cookie.split(";");
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split("=");
    if (name === REFERRAL_COOKIE_NAME) {
      return decodeURIComponent(value);
    }
  }
  return undefined;
};

/**
 * Get the stored referral code from cookies
 */
export const getReferralCookie = (): string | undefined => {
  // Early return for server-side rendering
  if (typeof window === "undefined" || typeof document === "undefined") {
    return undefined;
  }

  // Try js-cookie first
  const jsValue = Cookies.get(REFERRAL_COOKIE_NAME);

  if (jsValue) {
    return jsValue;
  }

  // Try fallback method
  const fallbackValue = getReferralCookieFallback();

  return fallbackValue;
};

/**
 * Get the stored referral URL from cookies
 */
export const getReferralUrlCookie = (): string | undefined => {
  // Early return for server-side rendering
  if (typeof window === "undefined" || typeof document === "undefined") {
    return undefined;
  }

  return Cookies.get(REFERRAL_URL_COOKIE_NAME);
};

/**
 * Get the stored referral short link from cookies
 */
export const getReferralShortLinkCookie = (): string | undefined => {
  // Early return for server-side rendering
  if (typeof window === "undefined" || typeof document === "undefined") {
    return undefined;
  }

  return Cookies.get(REFERRAL_SHORT_LINK_COOKIE_NAME);
};

/**
 * Set comprehensive referral data (code, URL, and short link)
 */
export const setReferralData = (data: {
  referralCode?: string;
  referralUrl?: string;
  shortLink?: string;
}): void => {
  const { referralCode, referralUrl, shortLink } = data;

  if (referralCode) {
    setReferralCookie(referralCode);
  }

  if (referralUrl) {
    setReferralUrlCookie(referralUrl);
  }

  if (shortLink) {
    setReferralShortLinkCookie(shortLink);
  }

  console.log("Comprehensive referral data set:", data);
};

/**
 * Get all referral data from cookies
 */
export const getAllReferralData = (): {
  referralCode?: string;
  referralUrl?: string;
  shortLink?: string;
} => {
  return {
    referralCode: getReferralCookie(),
    referralUrl: getReferralUrlCookie(),
    shortLink: getReferralShortLinkCookie(),
  };
};

/**
 * Clear the referral cookies (for testing)
 */
export const clearReferralCookies = (): void => {
  Cookies.remove(REFERRAL_COOKIE_NAME, { path: "/" });
  Cookies.remove(REFERRAL_URL_COOKIE_NAME, { path: "/" });
  Cookies.remove(REFERRAL_SHORT_LINK_COOKIE_NAME, { path: "/" });
  console.log("Referral cookies cleared");
};

/**
 * Track a referral link click by sending a request to the API with enhanced cookie data
 */
export const trackReferralClick = async (currentUrl: string): Promise<void> => {
  try {

    // Get token from localStorage if available
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;

    // Get short link from cookies for priority tracking
    const shortLink = getReferralShortLinkCookie();

    console.log(`Tracking referral click for URL: ${currentUrl}, shortLink: ${shortLink}`);

    // Send the tracking request to our Next.js API proxy endpoint
    const headers: HeadersInit = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Include both URL and shortLink for priority-based tracking
    const requestBody: { url: string; shortLink?: string } = { url: currentUrl };
    if (shortLink) {
      requestBody.shortLink = shortLink;
    }

    await fetch('/api/referrer-links/track-click', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log(`Referral click tracked successfully for URL: ${currentUrl}, shortLink: ${shortLink}`);
  } catch (error) {
    console.error(`Error tracking referral click for URL ${currentUrl}:`, error);
  }
};

/**
 * Check if URL has a referral code and save it
 */
export const checkAndSaveReferralCode = (): void => {
  // Only run in browser environment
  if (typeof window === "undefined") return;

  try {
    const urlParams = new URLSearchParams(window.location.search);
    const referralCode = urlParams.get("via");

    if (referralCode) {
      console.log(`Found referral code in URL: ${referralCode}`);
      // Get the current URL with the referral code
      const currentUrl = window.location.href;
      
      // Track the referral click regardless of cookie status
      trackReferralClick(currentUrl);
      
      // Get existing referral code to check if we should set new one
      const existingReferralCode = getReferralCookie();

      // Only set if no existing code (first-touch attribution)
      if (!existingReferralCode) {
        setReferralCookie(referralCode);
      }
      setReferralUrlCookie(currentUrl);
    } else {
      console.log("No referral code found in URL");
    }
  } catch (error) {
    console.error("Error checking or saving referral code:", error);
  }
};
