/**
 * Utility to handle API response errors in a centralized way
 */
import { store } from '@/store';
import { actions as authActions } from '@/features/auth/auth.slice';

/**
 * Handles error responses from API calls
 * @param response The fetch API response
 * @returns True if the error was handled, false otherwise
 */
export const handleApiError = (response: Response): boolean => {
  // Handle 401 Unauthorized errors
  if (response.status === 401) {
    // Dispatch an auth required action that will trigger the toast with Sign In button
    store.dispatch(
      authActions.setRequireAuth("You need to sign in to access this resource.")
    );
    return true;
  }
  
  // Handle 429 Too Many Requests errors
  if (response.status === 429) {
    // Dispatch action for upgrade required toast
    store.dispatch(
      authActions.setRequireUpgrade("Rate limit exceeded. Please upgrade your plan to continue.")
    );
    return true;
  }

  // Add other status code handlers here as needed
  // e.g., 403 Forbidden, 500 Server Error, etc.

  // Return false if no special handling was done
  return false;
};

/**
 * Redirects user to authentication page with current path as redirect parameter
 */
export const redirectToAuth = (): void => {
  if (typeof window !== "undefined") {
    const currentPath = window.location.pathname + window.location.search;
    window.location.href = `/authentication?redirect=${encodeURIComponent(currentPath)}`;
  }
};
