import React, { useState, useRef, useEffect } from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { IVideo } from "@/interfaces";

interface ThumbnailProps {
  video: IVideo;
  size?: {
    width: string;
    height: string;
  };
  className?: string;
}

const Thumbnail: React.FC<ThumbnailProps> = ({
  video,
  size = { width: "120px", height: "90px" },
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isIframeLoading, setIsIframeLoading] = useState(true);
  const [tikTokVideoUrl, setTikTokVideoUrl] = useState<string | null>(null);
  const [loadingTikTokVideo, setLoadingTikTokVideo] = useState(false);
  const [videoPopoverPosition, setVideoPopoverPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const popoverRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  // Reset loading state when video changes or popover opens
  useEffect(() => {
    if (isOpen) {
      setIsIframeLoading(true);
      
      // Safety timeout to hide loading indicator after 10 seconds
      const timeoutId = setTimeout(() => {
        setIsIframeLoading(false);
      }, 10000);
      
      return () => clearTimeout(timeoutId);
    }
  }, [video?.video_id, video?.platform, isOpen]);
  
  // Add effect to fetch TikTok video URL
  useEffect(() => {
    if (video?.platform === "tiktok" && video.video_id && isOpen && !tikTokVideoUrl) {
      setLoadingTikTokVideo(true);
      
      fetch(`/api/social-listenings/tiktok-download/${video.video_id}`)
        .then((res) => res.json())
        .then((data) => {
          if (data && data.play) {
            setTikTokVideoUrl(data.play);
            setIsIframeLoading(false);
          }
        })
        .catch((err) => {
          console.error("Error fetching TikTok video:", err);
          setIsIframeLoading(false);
        })
        .finally(() => setLoadingTikTokVideo(false));
    }
  }, [video?.platform, video?.video_id, isOpen, tikTokVideoUrl]);

  // Helper function to calculate optimal popover position
  const getPopoverPosition = (
    rect: DOMRect,
    options: {
      clickX?: number;
      clickY?: number;
      isMobile?: boolean;
    } = {}
  ) => {
    const { clickX, clickY, isMobile = false } = options;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const popoverWidth = 300;
    
    // Estimate popover height based on platform
    const estimatedHeight = video && video.platform === "tiktok" ? 600 : 300;
    
    // Mobile positioning (centered horizontally, below content)
    if (isMobile || viewportWidth < 768) {
      // Center horizontally with padding
      let xPos = Math.max(10, (viewportWidth - popoverWidth) / 2);
      
      // Position below the thumbnail
      let yPos = rect.bottom + 10;
      
      // Check if there's enough space below
      const spaceBelow = viewportHeight - yPos;
      
      // Adjust if not enough space below
      if (estimatedHeight > spaceBelow - 20) {
        yPos = Math.max(10, viewportHeight - estimatedHeight - 10);
      }
      
      return { x: xPos, y: yPos };
    }
    
    // Desktop click positioning
    if (clickX !== undefined && clickY !== undefined) {
      return {
        x: clickX,
        y: clickY + 20, // Position 20px below the click
      };
    }
    
    // Desktop hover positioning - right side of thumbnail with fallbacks
    let xPos = rect.right; // 10px gap from thumbnail
    let yPos = Math.max(10, rect.top); // Align with top of thumbnail
    
    // Check if showing to the right would go off screen
    if (xPos + popoverWidth > viewportWidth - 20) {
      xPos = Math.max(20, rect.left - popoverWidth - 10); // Show to the left instead
    }
    
    // Handle vertical positioning
    const spaceBelow = viewportHeight - rect.top;
    if (estimatedHeight > spaceBelow - 40) {
      yPos = Math.max(20, viewportHeight - estimatedHeight - 20);
      
      // Center vertically if it would be cut off
      if (yPos < 20) {
        yPos = Math.max(10, (viewportHeight - estimatedHeight) / 2);
      }
    }
    
    return { x: xPos, y: yPos };
  };

  const handleMouseEnter = () => {
    // Only trigger on desktop (non-touch devices)
    if (window.matchMedia("(hover: hover)").matches && triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setVideoPopoverPosition(getPopoverPosition(rect));
      setIsOpen(true);
    }
  };

  const handleMouseLeave = (e: React.MouseEvent) => {
    // Only trigger on desktop (non-touch devices)
    if (window.matchMedia("(hover: hover)").matches) {
      // Check if mouse is moving to the popover
      const toElement = e.relatedTarget as Node | null;
      if (toElement && popoverRef.current?.contains(toElement)) {
        return;
      }
      setIsOpen(false);
    }
  };

  // Create a clean click handler for both mobile and desktop
  const handleThumbnailClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event bubbling

    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      const isMobile = window.innerWidth < 768;
      
      // Pass click coordinates for desktop, or set isMobile flag for mobile
      setVideoPopoverPosition(
        getPopoverPosition(rect, {
          clickX: isMobile ? undefined : e.clientX,
          clickY: isMobile ? undefined : e.clientY,
          isMobile
        })
      );
    } else {
      // Fallback if reference element isn't available
      setVideoPopoverPosition({
        x: e.clientX,
        y: e.clientY + 20,
      });
    }

    setIsOpen(!isOpen);
  };

  const handleIframeLoaded = () => {
    setIsIframeLoading(false);
  };

  // Function to render appropriate video embed based on platform
  const renderVideoPreview = () => {
    if (video && !video.video_link) return null;

    switch (video.platform) {
      case "youtube":
        // Extract video ID from YouTube URL
        return (
          <div className="w-full max-h-[200px] overflow-hidden rounded-t-md relative">
            {isIframeLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-black border-t-transparent"></div>
              </div>
            )}
            <iframe
              width="100%"
              height="200"
              src={`https://www.youtube.com/embed/${video.video_id}?autoplay=1`}
              title={video.title}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              onLoad={handleIframeLoaded}
              className={isIframeLoading ? "opacity-0" : "opacity-100"}
            ></iframe>
          </div>
        );
      case "tiktok":
        // Use HTML5 video tag when tikTokVideoUrl is available
        return (
          <div
            className={`w-full h-[600px] overflow-hidden rounded-t-md relative`}
          >
            {(isIframeLoading || loadingTikTokVideo) && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-black border-t-transparent"></div>
              </div>
            )}
            <div className="relative w-full h-full">
              <div className="flex flex-col h-full">
                {tikTokVideoUrl ? (
                  <video
                    src={tikTokVideoUrl}
                    controls
                    autoPlay
                    style={{
                      width: "100%",
                      height: "calc(100% - 30px)",
                      objectFit: "contain",
                    }}
                    onLoadedData={() => setIsIframeLoading(false)}
                    className={isIframeLoading ? "opacity-0" : "opacity-100"}
                  />
                ) : (
                  <iframe
                    src={`https://www.tiktok.com/embed/v2/${video.video_id}?autoplay=1`}
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      width: "100%",
                      height: "100%",
                      border: "none",
                    }}
                    frameBorder="0"
                    allow="autoplay"
                    allowFullScreen
                    scrolling="no"
                    onLoad={handleIframeLoaded}
                    className={isIframeLoading ? "opacity-0" : "opacity-100"}
                  ></iframe>
                )}
                {video.channel_title && (
                  <div
                    className="flex items-center p-2 bg-secondary/50 border-b cursor-pointer hover:bg-gray-100"
                    onClick={() =>
                      window.open(
                        video.video_link,
                        "_blank",
                        "noopener,noreferrer"
                      )
                    }
                  >
                    <div className="flex-shrink-0 w-[50px] h-[50px] rounded-full overflow-hidden mr-3">
                      <img
                        src={video.channel_avatar || "/default-avatar.jpg"}
                        alt={video.channel_title || "Channel"}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = "/default-avatar.jpg";
                        }}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold truncate">
                        {video.channel_title}
                      </h3>
                      <div className="text-xs text-gray-500">
                        Posted:{" "}
                        {video.published_from
                          ? new Date(video.published_from).toLocaleDateString()
                          : "N/A"}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      default:
        // Fallback to showing the thumbnail with a play button overlay
        return (
          <div className="relative w-full h-[200px] rounded-t-md overflow-hidden">
            {isIframeLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-black border-t-transparent"></div>
              </div>
            )}
            <img
              src={video.thumbnail || "/default-image.jpg"}
              alt={video.title}
              className="w-full h-full object-cover"
              onLoad={() => setIsIframeLoading(false)}
              onError={(e) => {
                e.currentTarget.src = "/default-image.jpg";
                setIsIframeLoading(false);
              }}
            />
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
              <div className="w-12 h-12 rounded-full bg-white bg-opacity-80 flex items-center justify-center">
                <div className="w-0 h-0 border-t-[6px] border-t-transparent border-l-[12px] border-l-black border-b-[6px] border-b-transparent ml-1"></div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <>
      <div
        ref={triggerRef}
        className={`relative overflow-hidden rounded-md cursor-pointer ${className}`}
        style={{ width: size.width, height: size.height }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleThumbnailClick}
      >
        <img
          src={video.thumbnail || "/default-image.jpg"}
          alt={video.title}
          className="w-full h-full object-cover"
          onError={(e) => {
            e.currentTarget.src = "/default-image.jpg";
          }}
        />
      </div>
      
      {/* Video preview popover with position based on hover/click */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger className="hidden">
          <span />
        </PopoverTrigger>
        <PopoverContent
          ref={popoverRef}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          className="w-[300px] p-0 border shadow-lg overflow-hidden"
          align="center"
          side="bottom"
          style={
            videoPopoverPosition
              ? {
                  position: "fixed",
                  top: `${videoPopoverPosition.y}px`,
                  left: `${videoPopoverPosition.x}px`,
                  transform: "none",
                  zIndex: 50,
                  maxWidth: "100vw",
                  width:
                    window.innerWidth <= 300
                      ? `${window.innerWidth - 20}px`
                      : "300px",
                }
              : undefined
          }
        >
          <div className="flex flex-col">{renderVideoPreview()}</div>
        </PopoverContent>
      </Popover>
    </>
  );
};

export default Thumbnail;