"use client";

import * as React from "react";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  Drawer<PERSON>ontent,
  DrawerHeader,
  Drawer<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ICategory } from "@/interfaces";
import LinkHeader from "./LinkHeader";
import router from "next/router";
import CategorySelector from "./CategorySelector";
import { ModeToggle } from "../mode-toggle";
import { authActions } from "@/features/rootActions";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";
import { useDispatch, useSelector } from "react-redux";

export function NavDrawer({
  categories,
  activeNav,
  children,
}: {
  categories: ICategory[];
  activeNav: string;
  children: React.ReactNode;
}) {
  const [open, setOpen] = React.useState(false);
  const dispatch = useDispatch();
  const isAuth = useSelector(selectIsAuthenticated);

  const handleNavigation = () => {
    setOpen(false);
  };

  return (
    <Drawer direction="top" open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>{children}</DrawerTrigger>
      <DrawerContent>
        <div className="mx-auto w-full max-w-sm p-3">
          <DrawerHeader>
            <DrawerTitle>
              <div className="flex items-center justify-between">
                <h1
                  onClick={() => {
                    router.push("/");
                    handleNavigation();
                  }}
                  className="text-[28px] font-bold text-[#3861FB] cursor-pointer"
                >
                  Affitor
                </h1>
                <DrawerClose>
                  <Button
                    className="h-12 w-12 flex items-center md:justify-center justify-end"
                    variant="ghost"
                    size="icon"
                    aria-label="Close"
                  >
                    <X className="w-8 h-8" />
                  </Button>
                </DrawerClose>
              </div>
            </DrawerTitle>
          </DrawerHeader>
          <ModeToggle />
          <div className="pb-2">
            <div className="w-full space-y-5 flex flex-col">
              <div onClick={handleNavigation}>
                <LinkHeader label="Home" href="/" activeNav={activeNav} />
              </div>
              <div onClick={handleNavigation}>
                <LinkHeader label="Programs" href="/" activeNav={activeNav} />
              </div>
              <div onClick={handleNavigation}>
                <LinkHeader
                  label="Top Videos"
                  href="/top-videos"
                  activeNav={activeNav}
                />
              </div>
              <div onClick={handleNavigation}>
                <LinkHeader
                  label="Affiliate Hunt"
                  href="/hunt"
                  activeNav={activeNav}
                />
              </div>
              <Accordion type="single" collapsible className="p-0">
                <AccordionItem value="item-1" className="p-0">
                  <AccordionTrigger className="text-[16px] p-0 text-primary-foreground">
                    Categories
                  </AccordionTrigger>
                  <AccordionContent>
                    <div
                      className="flex flex-col space-y-2 max-h-[200px] overflow-y-auto pr-2 py-1"
                      onClick={handleNavigation}
                    >
                      <CategorySelector categories={categories} />
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
              {/* <LinkHeader label="Insights" href="/" activeNav={activeNav} /> */}
              <LinkHeader
                label="Pricing"
                href="/pricing"
                activeNav={activeNav}
              />
              {/* Add Sign In/Sign Out based on authentication status */}
              <div onClick={handleNavigation}>
                {!isAuth ? (
                  <LinkHeader
                    label="Sign In"
                    href="/authentication"
                    activeNav={activeNav}
                  />
                ) : (
                  <LinkHeader
                    label="Profile"
                    href="/profile"
                    activeNav={activeNav}
                  />
                )}
              </div>
              {/* <div onClick={handleNavigation}>
                <LinkHeader
                  label="Join Us"
                  href="/group"
                  activeNav={activeNav}
                />
              </div> */}
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
